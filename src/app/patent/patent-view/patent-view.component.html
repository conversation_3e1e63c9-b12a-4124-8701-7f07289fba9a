<div class="figma-container scrollbar-2024 content-color-base" *ngIf="selectedPatent" [ngClass]="'figma-screen-' + screenSizeAlias" [class.position-relative]="addComment" appPriorityPlusMenu>
  <div class="figma-header p-spacing-big">
    <div class="figma-header-menu content-body-medium d-flex justify-content-between">
      <div class="figma-header-menu-group d-flex justify-content-start align-items-center">
        <div class="m-l-spacing-md">
          <div id="button-patent-viewer-back" *ngIf="canBack" class="figma-header-menu-item button-main-tertiary-grey"
               (click)="backToSearch()"  [ngbTooltip]="backButtonTitle" tooltipClass="white-tooltip">
            <i class="fa-regular fa-chevron-left  m-r-spacing-md"></i> Back
          </div>
          <div *ngIf="!canBack" >
            <img ngSrc="assets/images/logo/Logo-stander-curves-over-white-bg.svg" alt="Octimine logo" class="cursor-pointer" width="146" height="42" (click)="backToSearch()">
          </div>
        </div>
      </div>
      <div class="figma-header-menu-group">
        <div class="p-t-spacing-none p-b-spacing-none position-absolute start-50 translate-middle-x">
          <div class="figma-dropdown patent-dropdown" id="patent-family-dropdown">
            <div class="figma-dropdown-btn p-y-spacing-sm p-x-spacing-md" id="button-patent-family-dropdown"  appDropdownToggle [closeOnClick]="true"
              tooltipClass="white-tooltip" data-intercom-target="family-dropdown">
              <span [ngClass]="getFlagIcon(activePublicationNumber)" *ngIf="!isFamilyView"></span>
              <span class="content-label-large d-flex flex-row flex-shrink-0 align-items-center">{{isFamilyView? 'Family representative':activePublicationNumber}}
                <span class="content-color-tertiary content-label-medium p-l-spacing-xx-s" *ngIf="isFamilyView"> (default) </span> <i *ngIf="isFamilyView" [ngbTooltip]="familyTooltip" tooltipClass="white-tooltip tooltip-medium" class="fa-regular fa-circle-info tooltip-icon m-l-spacing-xx-s"></i></span>
              <div class="dropdown-icon d-flex position-relative m-l-spacing-xx-s"></div>
            </div>
            <div  class="figma-dropdown-content radius-big" id="body-patent-family-dropdown">
              <div class="p-y-spacing-md p-x-spacing-sm overflow-auto">
                <div class="figma-dropdown-item figma-dropdown-item-hover m-b-spacing-big" [class.active]="isFamilyView" (click)="loadPublication($event, null,false)">
                  <div class="d-flex align-items-center">
                    <a href="javascript:void(0)" class="content-heading-h5">
                      Family representative <span class="content-color-tertiary content-label-medium">(default) <i [ngbTooltip]="familyTooltip" tooltipClass="white-tooltip tooltip-medium" class="fa-regular fa-circle-info tooltip-icon"></i></span>
                      <span *ngIf="hasLinksToBooleanSearch" class="button-main-tertiary-grey button-square button-xsmall m-l-spacing-x-s open-family-new-tab"
                            (click)="loadPublication($event, null, true)">
                        <i class="fa-regular fa-arrow-up-right-from-square fa-1x content-color-secondary"></i>
                      </span>
                      <ng-template #familyTooltip>
                        <div class="text-left ">
                          <div class="p-b-spacing-sm">The Family representative combines information from all publications within a patent family, offering a comprehensive default view.</div>
                          <div>To access detailed information from a specific publication, choose  the relevant family member.</div>
                        </div>
                      </ng-template>
                    </a>
                  </div>
                  <div class="d-block {{isFamilyView ? 'content-label-small': 'content-body-small'}} text-truncate m-t-spacing-sm" >{{patent.bibliographic.title}}</div>
                </div>
              <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-sm p-x-spacing-none p-y-spacing-none">Family members</div>
              <div class="figma-dropdown-item figma-dropdown-item-hover" *ngFor="let p of patent?.bibliographic?.also_published_as"
                   (click)="loadPublication($event, p,false)" [class.active]="!isFamilyView && activePublicationNumber === p">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex flex-column justify-content-start p-r-spacing-sm">
                    <a href="javascript:void(0)" class="content-heading-h5">
                      <span [ngClass]="getFlagIcon(p)"></span> {{ p }}
                      <span *ngIf="hasLinksToBooleanSearch"
                            class="button-main-tertiary-grey button-square button-xsmall m-l-spacing-x-s open-family-new-tab"
                            (click)="loadPublication($event, p, true)">
                          <i class="fa-regular fa-arrow-up-right-from-square fa-1x content-color-secondary"></i>
                        </span>
                    </a>
                    <div class="{{!isFamilyView && activePublicationNumber === p ? 'content-label-small': 'content-body-small'}} m-t-spacing-sm ellipsis-text-1">
                      {{ getFamilyMemberTitle(p) }}
                    </div>
                  </div>
                  <app-authority-legal-status name="{{getFamilyMemberLegalStatusIcon(p, false).name}}" size="sm"
                                              tooltip="{{getFamilyMemberLegalStatusIcon(p, false).tooltip}}"
                                              [authorities]="getFamilyMember(p)?.authority_legal_status" cssClass="cursor-pointer">
                  </app-authority-legal-status>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="figma-header-menu-group d-flex justify-content-end align-items-center content-body-medium ">
        <div class="priority-menu-bar w-100 d-flex">

          <app-user-avatars *ngIf="readDocumentUsers?.length" class="p-r-spacing-md border-0 border-r-1 border-r-subtle"
                            [users]="readDocumentUsers" [numberDisplayedUsers]="5"
                            [distanceBetweenAvatars]="24" size="small"
                            [userExtraInfoTemplate]="userExtraInfoTemplate"
                            [moreUsersExtraInfoTemplate]="moreUsersExtraInfoTemplate"
                            [activeUserIds]="[userService?.getUser()?.profile?.id]">
            <ng-template #userExtraInfoTemplate let-user="user">
              <div class="content-body-xsmall content-color-tertiary p-t-spacing-xx-s">
                <ng-container *ngIf="isMe(user)">Viewing now</ng-container>
                <ng-container *ngIf="!isMe(user)">
                  Last viewed {{ getReadDocument(user).last_read.toString() | timeReadable }}
                </ng-container>
              </div>
            </ng-template>

            <ng-template #moreUsersExtraInfoTemplate let-user="user">
              <div class="content-body-xsmall content-color-quartary p-t-spacing-xx-s">
                <ng-container *ngIf="isMe(user)">Viewing now</ng-container>
                <ng-container *ngIf="!isMe(user)">
                  Last viewed {{ getReadDocument(user).last_read.toString() | timeReadable }}
                </ng-container>
              </div>
            </ng-template>
          </app-user-avatars>

          <div class="d-flex justify-content-end align-items-center" data-intercom-target="family-actions">
            <div class="d-flex flex-row  overflow-y-auto priority-menu p-l-spacing-sm">
              <span  class="button-main-tertiary-grey button-square button-small"
              *ngIf="userService.isNotExternalUser() && hasWorkflowFeature()"
              ngbTooltip="Share" tooltipClass="white-tooltip"
              (click)="onSharePatentClicked(selectedPatent)">
                <i class="fa-regular fa-share-nodes"></i>
              </span>
              <span class="button-main-tertiary-grey button-square button-small"
              ngbTooltip="Add to collection" tooltipClass="white-tooltip" *ngIf="userService.isNotExternalUser()"
              (click)="onSaveToCollectionClicked($event)">
                <i class="fa-regular fa-folder-plus"></i>
              </span>
              <span class="button-main-tertiary-grey button-square button-small" *ngIf="userService.isNotExternalUser()"
              (click)="downloadPdf($event)"
              ngbTooltip="Open PDF" tooltipClass="white-tooltip">
                <i class="fa-regular fa-file-pdf"></i>
              </span>
              <span class="button-main-tertiary-grey button-square button-small"
              *ngIf="userService.isNotExternalUser()"
              ngbTooltip="Search similar patents" tooltipClass="white-tooltip"
              (click)="openPublication('/search/?publication='+activePublicationNumber)">
                <i class="fa-regular fa-magnifying-glass-arrow-right"></i>
              </span>
              <span class="button-main-tertiary-grey button-square button-small"
              *ngIf="isSemanticReferral && showBackButton"
              ngbTooltip="Add to current search" tooltipClass="white-tooltip" [ngClass]="{'disabled': !canAddNumbersToSearch()}"
              (click)="onAddToSearchClicked()">
                <i class="fa-regular fa-magnifying-glass-plus"></i>
              </span>
            </div>

            <span class="priority-menu-more figma-dropdown">
              <div class="figma-dropdown-btn button-main-tertiary-grey button-square button-small" appDropdownToggle>
                <i class="fa-regular fa-ellipsis-vertical menu-icon"></i>
                <div  class="figma-dropdown-content p-spacing-md figma-dropdown-content-right radius-big text-start priority-menu-dropdown">
                </div>
              </div>
            </span>
            <a href="javascript:void(0)" class="figma-dropdown header-menu-dropdown">
                <div class="figma-dropdown-btn button-main-tertiary-grey button-small" appDropdownToggle [closeOnClick]="true" ngbTooltip="View in" tooltipClass="white-tooltip" >
                  <i class="fa-regular fa-eye"></i><i class="fa-regular fa-chevron-down figma-dropdown-icon "></i>
                </div>
                <div  class="figma-dropdown-content p-spacing-md figma-dropdown-content-right radius-big">
                  <div class="figma-dropdown-item figma-dropdown-item-hover p-y-spacing-x-s p-x-spacing-big" (click)="openEspacenet()" >View in Espacenet <i class="fa-regular fa-arrow-up-right-from-square menu-item-right"></i></div>
                  <div class="figma-dropdown-item figma-dropdown-item-hover p-y-spacing-x-s p-x-spacing-big" (click)="openGlobalDossier()">View in Global Dossier <i class="fa-regular fa-arrow-up-right-from-square menu-item-right"></i></div>
                </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="figma-body p-y-spacing-sm p-x-spacing-big">
    <div class="figma-patent-container radius-lg p-spacing-none">
      <div class="figma-patent-menu justify-content-between p-spacing-md content-label-small" data-intercom-target="patent-menu">
        <div class="figma-header-menu-group d-flex">
          <div class="figma-header-menu-item">
            <div class="figma-dropdown patent-content-dropdown d-inline-block">
              <div class="button-main-tertiary-grey button-square button-small" appDropdownToggle [closeOnClick]="true"
              ngbTooltip="Content" tooltipClass="white-tooltip" data-intercom-target="menu-navigation">
                <i class="figma-dropdown-icon fa-solid fa-bars invert-icon"></i>
              </div>
              <div class="figma-dropdown-content w-max-content radius-big p-spacing-md">
                <div class="figma-dropdown-label content-heading-h4 p-x-spacing-big "><span>Content</span></div>
                <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium p-x-spacing-big " (click)="scrollTo('sec-bibliographic-info')">Bibliographic data</div>
                <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium p-x-spacing-big " (click)="scrollTo('sec-abstract')">Abstract</div>
                <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium p-x-spacing-big " (click)="scrollTo('sec-claims')">Claims</div>
                <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium p-x-spacing-big " (click)="scrollTo('sec-description')">Description</div>
                <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium p-x-spacing-big " (click)="scrollTo('sec-citations')">Citations and references</div>
                <div class="figma-dropdown-item figma-dropdown-item-hover content-body-medium p-x-spacing-big " (click)="scrollTo('sec-legal-events')">Legal events</div>
              </div>
            </div>
          </div>
          <div class="figma-header-menu-divider p-y-spacing-sm p-x-spacing-md m-l-spacing-md"></div>
        </div>
        <div class="figma-header-menu-group d-flex patent-menu-bar priority-menu-bar" >
          <div class="d-flex flex-row  ms-auto overflow-y-auto  priority-menu">
            <span id="button-oct-ai" *ngIf="userService.hasOctiAiFeature()" class="button-main-tertiary button-small flex-shrink-0 " [class.active]="sideSection === sideSectionEnum.OCTI_AI"
                  (click)="toggleSideSection(sideSectionEnum.OCTI_AI)"
                  ngbTooltip="Get answers, understand patents, and more." tooltipClass="white-tooltip">
              <i class="fa-regular octi-icon menu-icon"></i><span class="p-x-spacing-xx-s menu-text">Octi AI</span>
            </span>
            <span id="button-patent-highlights" *ngIf="userService.isNotExternalUser()" class="button-main-tertiary-grey button-small flex-shrink-0 " [class.active]="sideSection === sideSectionEnum.HIGHLIGHTS"
                  (click)="patentHighlights.openColorsPopper($event, colorsPopperSourceEnum.TOP_MENU)"
                  ngbTooltip="Highlight" tooltipClass="white-tooltip button-small-tooltip">
              <span [style.background-color]="patentHighlights.selectedColor"><i class="fa-regular fa-highlighter menu-icon"></i></span>
              <span class="p-x-spacing-xx-s menu-text">Highlight</span>
            </span>
            <span id="button-patent-comments" *ngIf="userService.isNotExternalUser()" class="button-main-tertiary-grey button-small flex-shrink-0 " [class.active]="sideSection === sideSectionEnum.COMMENTS"
                  (click)="toggleSideSection(sideSectionEnum.COMMENTS);" ngbTooltip="Comments" tooltipClass="white-tooltip button-small-tooltip">
              <i class="fa-regular fa-message menu-icon"></i>
              <span class="p-x-spacing-xx-s menu-text">
                <span *ngIf="commentCount">
                  {{ commentCount }}
                </span>
                <span>{{'Comment' | pluralize : commentCount}}</span>
              </span>
            </span>
            <span id="button-patent-ratings" *ngIf="hasWorkflowFeature()"
                  class="button-main-tertiary-grey button-small flex-shrink-0 "
                  [class.active]="sideSection === sideSectionEnum.RATINGS"
                  (click)="toggleSideSection(sideSectionEnum.RATINGS);"
                  ngbTooltip="Ratings" tooltipClass="white-tooltip button-small-tooltip">
              <i class="fa-regular fa-circle-star menu-icon"></i>
              <span class="p-x-spacing-xx-s menu-text">
                <span *ngIf="countDoneRatings">
                  {{ countDoneRatings }}
                </span>
                <span>{{ 'Rating' | pluralize : countDoneRatings }}</span>
              </span>
            </span>
            <span id="button-patent-relevant-parts" class="button-main-tertiary-grey button-small flex-shrink-0 " [class.active]="sideSection === sideSectionEnum.RELEVANCE"
                  (click)="toggleSideSection(sideSectionEnum.RELEVANCE)" *ngIf="hasSmartHighlights(selectedPatent)" ngbTooltip="Relevant parts" tooltipClass="white-tooltip button-small-tooltip">
              <i class="fa-regular fa-crosshairs menu-icon"></i><span class="p-x-spacing-xx-s menu-text">Relevant parts</span>
            </span>
            <span id="button-patent-current-list" class="button-main-tertiary-grey button-small flex-shrink-0 " *ngIf="hasResultList" [class.active]="sideSection === sideSectionEnum.LIST" (click)="toggleSideSection(sideSectionEnum.LIST)"
              [ngbTooltip]="sideSectionEnum.LIST" tooltipClass="white-tooltip button-small-tooltip">
              <i class="fa-regular fa-list-ul menu-icon"></i>
              <span class="p-x-spacing-xx-s menu-text">{{sideSectionEnum.LIST}}</span>
            </span>
          </div>
          <span class="priority-menu-more figma-dropdown">
            <div class="figma-dropdown-btn button-main-tertiary-grey button-square button-small" appDropdownToggle>
              <i class="fa-regular fa-ellipsis-vertical menu-icon"></i>
            </div>
            <div  class="figma-dropdown-content p-spacing-md figma-dropdown-content-right radius-big text-start priority-menu-dropdown">
            </div>
          </span>
          <div class="figma-header-menu-divider p-y-spacing-x-s m-x-spacing-md">&nbsp;</div>

          <div id="btnSearchBox" class="button-main-tertiary-grey button-square button-small" (click)="openSearchBox()"
            ngbTooltip="Find in patent" tooltipClass="white-tooltip">
            <i class="fa-regular fa-magnifying-glass menu-icon"></i>
          </div>
        </div>
      </div>

      <div id="figma-patent-body" class="figma-patent-body p-spacing-none" [class.loading-patent]="isLoadingPatent"
           (scroll)="onScroll($event)">
        <div class="figma-patent-content">
          <div class="p-t-spacing-md p-r-spacing-x-lg p-b-spacing-md p-l-spacing-xxx-big w-100">
            <div id="sec-title-info" class="patent-title-info">
              <div class="content-heading-h4 w-100 p-b-spacing-big" [innerHtml]="patentTitle">
              </div>
              <div #containerTags class="tags w-100" *ngIf="userService.hasTagFeature()">
                <app-tags-display [patent]="selectedPatent" [container]="containerTags" [canManageTags]="userService.isNotExternalUser()"
                                  [showIcon]="false" [updateTagsOnResize]="true" [storeService]="storeService"
                                  (tagsChange)="onPatentTagsChanged($event)">
                </app-tags-display>
              </div>
            </div>
            <div class="m-y-spacing-md w-100">
              <div class="figma-divider-dashed"></div>
            </div>
            <div id="sec-bibliographic-info" class="nav-section overflow-hidden" data-intercom-target="bibliographic-info">
              <div class="bibliographic-info-column">
                <div class="bibliographic-info-column-left">
                  <div class="field-info">
                    <div class="field-title">Publication number</div>
                    <div class="content-label-large"><span [ngClass]="getFlagIcon(activePublicationNumber)" class="m-r-spacing-xx-s"></span>{{activePublicationNumber}}</div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Legal status</div>
                    <div class="d-flex align-items-center gap-spacing-sm">
                      <app-authority-legal-status
                        name="{{getFamilyMemberLegalStatusIcon(activePublicationNumber, isFamilyView && !storeService.isPublications).name}}"
                        tooltip="{{getFamilyMemberLegalStatusIcon(activePublicationNumber, isFamilyView && !storeService.isPublications).tooltip}}"
                        [authorities]="selectedPatent?.bibliographic?.authority_legal_status" size="md">
                      </app-authority-legal-status>
                      <span *ngIf="userService.hasMonitor" class="button-main-tertiary-grey button-small tracking-legal-status-btn" (click)="openLegalStatusTrackingModal(activePublicationNumber)" [class.disabled]="!isTrackingAvailable(activePublicationNumber)" [ngbTooltip]="isLegalStatusTracked(activePublicationNumber) ? 'Tracking legal status changes' : 'Track legal status changes'" tooltipClass="white-tooltip"><i class=" fa-bell-on" [ngClass]="isLegalStatusTracked(activePublicationNumber) ? 'fa-solid content-color-active': 'fa-regular'"></i></span>
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title d-flex gap-1">Priority date <i ngbTooltip="The earliest application date of the entire patent family." tooltipClass="white-tooltip" class="fa-regular fa-circle-info tooltip-icon"></i></div>
                    <div class="content-label-large" *ngIf="patent?.bibliographic?.priority_date else notAvailable">
                      {{patent?.bibliographic?.priority_date | dateFormat: 'ShortDate'}}
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Application date</div>
                    <div class="content-label-large" *ngIf="publication?.bibliographic?.application_date else notAvailable">
                      {{publication?.bibliographic?.application_date | dateFormat: 'ShortDate'}}
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Publication date</div>
                    <div class="content-label-large" *ngIf="publication?.bibliographic?.publication_date else notAvailable">
                      {{publication?.bibliographic?.publication_date | dateFormat: 'ShortDate'}}
                    </div>
                  </div>
                  <div *ngIf="isPublicationView && getPatentLegalStatus(activePublicationNumber) !== patentLegalStatusEnum.DEAD && publication?.bibliographic?.anticipated_expiration_date"
                       class="field-info">
                    <div class="field-title d-flex gap-1">
                      Expiration date
                      <i ngbTooltip="Estimated expiration date of the patent rights, based on current law."
                         tooltipClass="white-tooltip" class="fa-regular fa-circle-info tooltip-icon"></i>
                    </div>
                    <div class="content-label-large">
                      {{publication?.bibliographic?.anticipated_expiration_date | dateFormat: 'ShortDate'}}
                    </div>
                  </div>
                </div>
                <div class="bibliographic-info-column-right">
                  <div class="field-info">
                    <div class="field-title">Other authorities</div>
                    <div class="content-label-large">
                      <ng-container *ngIf="!patent?.bibliographic?.authorities.length" [ngTemplateOutlet]="notAvailable"></ng-container>

                      <span class="flag-authority-group">
                        <span *ngFor="let authority of patent?.bibliographic?.authorities"
                        [ngbPopover]="tooltipAuthority" triggers="manual" container="body" #pAuthority="ngbPopover"
                        [autoClose]="false" popoverClass="white-popover authority-popover scrollbar-2024" appSelectableTooltip [selectableTooltipPopover]="pAuthority">
                        <ng-template #tooltipAuthority>
                          <span class="content-heading-h6 content-color-tertiary m-b-spacing-sm d-block">Authority publications</span>
                          <div class="authority-popover-publication align-items-center" *ngFor="let p of getPublicationsAuthority(authority)">
                            <span class="d-flex align-items-center gap-spacing-xx-s">
                              <span class="cursor-pointer" [ngClass]="getFlagIcon(p)" (click)="loadPublication($event, p, false)"></span>
                              <span class="content-heading-h5 cursor-pointer" (click)="loadPublication($event, p, false)">{{p}}</span>
                              <span *ngIf="hasLinksToBooleanSearch" class="button-main-tertiary-grey button-square button-xsmall m-l-spacing-x-s open-family-new-tab"
                              (click)="loadPublication($event, p, true)">
                                <i class="fa-regular fa-arrow-up-right-from-square fa-1x content-color-secondary"></i>
                              </span>
                            </span>
                            <app-authority-legal-status name="{{getFamilyMemberLegalStatusIcon(p, false).name}}" size="sm"
                            tooltip="{{getFamilyMemberLegalStatusIcon(p, false).tooltip}}" [authorities]="getFamilyMember(p)?.authority_legal_status"
                            [parentPopover]="pAuthority" cssClass="cursor-pointer">
                            </app-authority-legal-status>
                          </div>
                        </ng-template>
                          <span class="flag-tag-authority">
                            <span [ngClass]="getFlagIcon(authority)"></span>{{authority}}
                            <span class="content-color-tertiary" *ngIf="getAmountOfPublicationByAuthority(authority) > 1">({{getAmountOfPublicationByAuthority(authority)}})</span>
                          </span>
                        </span>
                      </span>
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Applicants
                      <a href="javascript:void(0)" class="button-main-link p-spacing-none"
                         appSelectableTooltip [selectableTooltipPopover]="p3"
                         #p3="ngbPopover" [ngbPopover]="popoverAllApplicants"
                         triggers="manual" [autoClose]="'outside'" popoverClass="white-popover">(see originals)</a>
                    </div>
                    <div class="content-label-large d-block">
                      <ng-container [ngTemplateOutlet]="linkTemplate" [ngTemplateOutletContext]="{ values: publication?.bibliographic?.applicants, field: 'APPLICANTS'}" ></ng-container>
                      <ng-template #popoverAllApplicants>
                        <div>
                          <div class="popover-title">Original applicants <a href="javascript:void(0)" class="button-main-tertiary-grey button-square button-xsmall popover-close-icon" (click)="p3.close()"></a></div>
                          <div class="popover-caption">{{ORIGINAL_TOOLTIP_TEXT}}</div>
                          <div class="popover-divider m-t-spacing-xx-s"></div>
                          <div class="popover-descriptions m-y-spacing-md">
                            {{publication?.bibliographic?.applicants_original}}
                          </div>
                        </div>
                      </ng-template>
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Inventors</div>
                    <div class="content-label-large d-block">
                      <ng-container [ngTemplateOutlet]="linkTemplate" [ngTemplateOutletContext]="{values: publication?.bibliographic?.inventors, field: 'INVENTORS'}"></ng-container>
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Owners
                      <a href="javascript:void(0)" class="button-main-link p-spacing-none"
                         appSelectableTooltip [selectableTooltipPopover]="p1"
                         #p1="ngbPopover" [ngbPopover]="popoverAllOwners"
                         triggers="manual" [autoClose]="'outside'" popoverClass="white-popover">(see originals)</a></div>
                    <div class="content-label-large d-block">
                      <ng-container [ngTemplateOutlet]="linkTemplate" [ngTemplateOutletContext]="getOwners('owners')"></ng-container>
                      <ng-template #popoverAllOwners>
                        <div>
                          <div class="popover-title">Original names <a href="javascript:void(0)" class="button-main-tertiary-grey button-square button-xsmall popover-close-icon" (click)="p1.close()"></a></div>
                          <div class="popover-caption">{{ORIGINAL_TOOLTIP_TEXT}}</div>
                          <div class="popover-divider m-t-spacing-xx-s"></div>
                          <div class="popover-descriptions m-y-spacing-md">
                            {{getOwnersLabels('assignees_original').join(', ') }}
                          </div>
                        </div>
                      </ng-template>
                    </div>
                  </div>
                  <div class="field-info" *ngIf="patentService.showSubsidiary(patent)">
                    <div class="field-title d-flex gap-1">{{ 'Ultimate owner '| pluralize: patent.bibliographic.owners.length }} <i tooltipClass="tooltip-medium"  ngbTooltip="The ultimate owner is the holding company of the patent owner at the top of its hierarchy structure, which ultimately owns this patent." class="fa-regular fa-circle-info tooltip-icon"></i></div>
                    <div class="content-label-large">
                      <ng-container [ngTemplateOutlet]="linkTemplate" [ngTemplateOutletContext]="{values: getOwnersValues('ultimate_owners'), field: 'OWNER_IDS', labels: getOwnersLabels('ultimate_owners')}"></ng-container>
                    </div>
                  </div>
                </div>
              </div>
              <div class="bibliographic-info-column" [@collapse]="lessBibliographicInfo">
                <div class="bibliographic-info-column-left">
                  <div class="field-info">
                    <div class="field-title">Application number</div>
                    <div class="content-label-large" *ngIf="publication?.bibliographic?.application_number else notAvailable">
                      {{publication?.bibliographic?.application_number}}
                    </div>
                  </div>
                  <div class="field-info" *ngIf="isFamilyView">
                    <div class="field-title d-flex gap-1">Cites <i ngbTooltip="Number of previous patents this family cites." tooltipClass="white-tooltip" class="fa-regular fa-circle-info tooltip-icon"></i></div>
                    <div class="content-label-large"><a href="javascript:void(0)" (click)="scrollTo('sec-citations')" class="button-main-link p-spacing-none">{{patent?.analytics?.citation_forward_count || 0}} Documents</a></div>
                  </div>
                  <div class="field-info" *ngIf="isFamilyView">
                    <div class="field-title d-flex gap-1">Cited by <i ngbTooltip="Number of subsequent patents this family is cited by." tooltipClass="white-tooltip" class="fa-regular fa-circle-info tooltip-icon"></i></div>
                    <div class="content-label-large"><a href="javascript:void(0)" (click)="scrollTo('sec-citations')" class="button-main-link p-spacing-none">{{patent?.analytics?.citation_backward_count || 0}} Documents</a></div>
                  </div>
                </div>
                <div class="bibliographic-info-column-right">
                  <div class="field-info">
                    <div class="field-title">IPC codes</div>
                    <div class="content-label-medium d-flex gap-spacing-sm m-spacing-xx-s flex-wrap">
                      <ng-container *ngIf="!patent?.bibliographic?.ipc || patent?.bibliographic?.ipc?.length === 0" [ngTemplateOutlet]="notAvailable"></ng-container>
                      <span *ngFor="let c of patent?.bibliographic?.ipc" [class.order-first]="publication?.bibliographic?.ipc?.includes(c)"
                            [hidden]="!publication?.bibliographic?.ipc?.includes(c) && !showIpcFamily"
                            appSelectableTooltip [selectableTooltipPopover]="pIpc">
                        <ng-template #tooltipTemp>
                          <div class="classification-popover-body">
                            <a href="javascript:void(0)" class="button-main-tertiary-grey button-square button-xsmall popover-close-icon"></a>
                            <div [innerHTML]="getDescription(false, 'ipc', c)"></div>
                          </div>
                        </ng-template>
                        <a [class.no-link]="!hasLinksToBooleanSearch" routerLink="/boolean" target="_blank" [ngClass]="publication?.bibliographic?.ipc?.includes(c) ?
                                                                            'tag-label-primary tag-label-primary-outline' :
                                                                            'tag-label-secondary tag-label-secondary-outline'"
                           [queryParams]="{field: 'IPC', value: c}"
                           [ngbPopover]="tooltipTemp" triggers="manual" #pIpc="ngbPopover" container="body"
                           [autoClose]="'outside'" popoverClass="white-popover classification-popover scrollbar-2024-sm">{{ c }}</a>
                      </span>
                      <span *ngIf="patent?.bibliographic?.ipc?.length > 0 && (!publication?.bibliographic?.ipc || publication?.bibliographic?.ipc?.length !== patent?.bibliographic?.ipc?.length)">
                        <a href="javascript:void(0)" class="tag-label-secondary tag-label-secondary-outline" (click)="showIpcFamily=!showIpcFamily">{{showIpcFamily? 'Hide':'Show'}} all family</a>
                      </span>
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">CPC codes</div>
                    <div class="content-label-medium d-flex gap-spacing-sm m-spacing-xx-s flex-wrap">
                      <ng-container *ngIf="!patent?.bibliographic?.cpc || patent?.bibliographic?.cpc?.length === 0" [ngTemplateOutlet]="notAvailable"></ng-container>
                      <span *ngFor="let c of patent?.bibliographic?.cpc" [class.order-first]="publication?.bibliographic?.cpc?.includes(c)"
                            [hidden]="!publication?.bibliographic?.cpc?.includes(c) && !showCpcFamily"
                            appSelectableTooltip [selectableTooltipPopover]="pCpc">
                        <ng-template #tooltipTemp>
                          <div class="classification-popover-body">
                            <a href="javascript:void(0)" class="button-main-tertiary-grey button-square button-xsmall popover-close-icon" (click)="pCpc.close()"></a>
                            <div [innerHTML]="getDescription(false, 'cpc', c)"></div>
                          </div>
                        </ng-template>
                        <a [class.no-link]="!hasLinksToBooleanSearch" routerLink="/boolean" target="_blank" [ngClass]="publication?.bibliographic?.cpc?.includes(c) ?
                                                                            'tag-label-primary tag-label-primary-outline' :
                                                                            'tag-label-secondary tag-label-secondary-outline'"

                           [queryParams]="{field: 'CPC', value: c}"
                           [ngbPopover]="tooltipTemp" triggers="manual" container="body" #pCpc="ngbPopover"
                           [autoClose]="'outside'" popoverClass="white-popover classification-popover scrollbar-2024-sm">{{ c }}</a>
                      </span>
                      <span *ngIf="patent?.bibliographic?.cpc?.length > 0 && (!publication?.bibliographic?.cpc || publication?.bibliographic?.cpc?.length !== patent?.bibliographic?.cpc?.length)">
                        <a href="javascript:void(0)" class="tag-label-secondary tag-label-secondary-outline" (click)="showCpcFamily=!showCpcFamily">{{showCpcFamily? 'Hide':'Show'}} all family</a>
                      </span>
                    </div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Main technology area</div>
                    <div class="content-label-large" *ngIf="selectedPatent?.bibliographic?.tech_areas?.length else notAvailable">{{ patentTableService.getTechAreas(selectedPatent) }}</div>
                  </div>
                  <div class="field-info">
                    <div class="field-title">Technology fields</div>
                    <div class="content-label-large" *ngIf="selectedPatent?.bibliographic?.tech_fields?.length else notAvailable">{{patentTableService.getTechFields(selectedPatent)}}</div>
                  </div>
                  <div class="field-info" *ngIf="publication?.bibliographic?.priority_claims">
                    <div class="field-title">Priority claims</div>
                    <div class="content-label-large">{{getPriorityClaims()}}</div>
                  </div>
                </div>
              </div>
              <a href="javascript:void(0)" class="button-main-link p-spacing-none content-label-large" (click)="lessBibliographicInfo=!lessBibliographicInfo">See {{lessBibliographicInfo? 'more':'less'}} bibliographic info <i class="fa " [ngClass]="lessBibliographicInfo? 'fa-angle-down':'fa-angle-up'"></i></a>
            </div>
            <div class="m-y-spacing-md w-100">
              <div class="figma-divider-dashed"></div>
            </div>

            <div id="patent-result" *ngIf="!isLoadingPatent" data-intercom-target="patent-text">
              <div class="figma-container-sm " [class.comments-highlighted]="sectionComments?.length>0" (mouseenter)="isOverTextArea = true" (mouseleave)="isOverTextArea = false">
                <div id="sec-abstract" class="nav-section p-t-spacing-big" [ngClass]="{'noselect': blockSelection && mouseDownElementId != 'abstract-text'}">
                  <div class="d-flex m-b-spacing-md">
                    <span class="button-main-tertiary-grey button-square button-small content-heading-h4" (click)="showAbstract=!showAbstract">
                      <i class="fa-solid" [ngClass]="showAbstract ? 'fa-caret-down': ' fa-caret-right'"></i>
                    </span>
                    <span class="content-heading-h3 p-x-spacing-xx-s">Abstract</span>
                  </div>
                  <div class="abstract-text content-body-medium p-l-spacing-lg" [hidden]="!showAbstract">
                    <ng-container *ngIf="patentAbstract else notAvailableMessage">
                      <div id="abstract-text" *ngIf="patentAbstract" [appInnerHtml]="patentAbstract | safeHtml: '<br/>' : 'N/A'"></div>
                      <div class="content-overline-3 content-color-tertiary noselect mt-3" *ngIf="selectedPatent?.bibliographic.abstract && selectedPatent?.bibliographic.abstract_publication_number">
                        Abstract from {{selectedPatent?.bibliographic.abstract_publication_number}}
                      </div>
                    </ng-container>
                  </div>
                </div>

                <div id="sec-claims" class="nav-section m-y-spacing-xxx-big" [ngClass]="{'noselect': blockSelection && mouseDownElementId != 'claims-text' }">
                  <div class="d-flex m-b-spacing-xx-big" data-intercom-target="claims-section">
                    <span class="button-main-tertiary-grey button-square button-small content-heading-h4" (click)="toggleClaims()">
                      <i class="fa-solid" [ngClass]="showClaims ? 'fa-caret-down': ' fa-caret-right'"></i>
                    </span>
                    <span class="content-heading-h3 p-l-spacing-xx-s p-b-spacing-big">
                      Claims
                    </span>
                  </div>
                  <div class="full-text content-body-medium p-l-spacing-lg" [ngClass]="{'obfuscated': selectedPatent?.general?.obfuscated}" [hidden]="!showClaims">
                    <ng-container *ngIf="patentClaims else notAvailableMessage">
                      <app-tree-view-claims *ngIf="showSmartHighlight" [showClaimsTree]="showClaimsTree" [claims]="patentClaims" [smartHighlightItems]="getSmartHighlights(selectedPatent)?.claims" [figmaClass]="true"></app-tree-view-claims>
                      <app-tree-view-claims *ngIf="!showSmartHighlight" [showClaimsTree]="showClaimsTree" [claims]="patentClaims" [figmaClass]="true"></app-tree-view-claims>
                      <div class="content-overline-3 content-color-tertiary mt-3" *ngIf="selectedPatent?.fulltext?.claims && selectedPatent?.fulltext.claims_publication_number">
                        Claims from {{selectedPatent?.fulltext.claims_publication_number}}
                      </div>
                    </ng-container>
                  </div>
                </div>

                <div id="sec-description" class="nav-section" [ngClass]="{'noselect': blockSelection && mouseDownElementId != 'description-text'}">
                  <div class="d-flex">
                    <span class="button-main-tertiary-grey button-square button-small content-heading-h4" (click)="toggleDescription()">
                      <i class="fa-solid" [ngClass]="showDescription ? 'fa-caret-down': ' fa-caret-right'"></i>
                    </span>
                    <span class="content-heading-h3 p-l-spacing-xx-s">
                      Description
                    </span>
                  </div>
                  <div class="full-text content-body-medium" [ngClass]="{'obfuscated': selectedPatent?.general?.obfuscated}" [hidden]="!showDescription"
                    [class]="patentDescription ? 'p-l-spacing-xx-big' : 'p-l-spacing-lg m-t-spacing-lg'">
                    <ng-container *ngIf="patentDescription else notAvailableMessage">
                      <div id="description-text" class="figma-innerHTML"
                           [appInnerHtml]="patentDescription | safeHtml: '<br/>' : 'N/A'"
                           [figmaClass]="true" [displayHeadingSections]="true" [displaySectionAnchors]="true"
                           sectionCollapseClass="button-main-tertiary-grey button-square button-small"
                           [smartHighlightItems]="getSmartHighlights(selectedPatent)?.description"
                           appParagraphNumber>
                      </div>
                      <div class="content-overline-3 content-color-tertiary mt-3 ps-3" *ngIf="selectedPatent?.fulltext?.description && selectedPatent?.fulltext.description_publication_number">
                        Description from {{selectedPatent?.fulltext.description_publication_number}}</div>
                    </ng-container>

                  </div>
                </div>
                <div class="comments-highlighted-container" *ngIf="sectionComments?.length>0"
                     [class.invisible]="sideSection" style="height: 100%; width: 1px">
                  <div class="comments-highlighted-note button-main-tertiary-grey button-small" id="dp-{{c.id}}" *ngFor="let c of sectionComments"
                    [ngbPopover]="popoverCommentTemplate" appSelectableTooltip [selectableTooltipPopover]="notePopover" #notePopover="ngbPopover"
                    popoverClass="white-popover comment-tooltip" triggers="manual" [autoClose]="'outside'"
                    (mouseleave)="onMouseLeaveComment(c)" (mouseover)="onMouseOverComment(c)" (mouseenter)="onMouseOverComment(c)" (click)="onCommentNoteClick(c)">
                    {{1+(c.replies?.length || 0)}}

                    <ng-template #popoverCommentTemplate>
                      <div class="comments-user-mentioned">
                        <div class="d-flex w-100 justify-content-between align-items-center mb-2 gap-2">
                          <app-user-avatar [user]="c.user" [hasSubTitle]="false" size="xsmall" class="comment-avatar"></app-user-avatar>
                          <div class="d-flex flex-column flex-grow-1">
                            <div class="content-heading-h5 comment-title">{{ c.user | userTitle }}</div>
                            <div class="content-heading-h7 content-color-tertiary">{{ c.created_at | timeReadable }} </div>
                          </div>
                          <i class="fa-regular fa-lock  m-r-spacing-sm" *ngIf="c.private"></i>
                        </div>
                        <div class="content-body-small d-flex flex-column justify-content-start align-items-start">
                          <span [innerHTML]="c.comment | safeHtml: null: null: {allowedTags: []} | tagParser: '14.6px/22px' : true : '@' | bypassSecurity: 'html'"></span>
                        </div>
                      </div>
                    </ng-template>
                  </div>
                </div>
              </div>

              <div id="sec-citations" class="nav-section p-t-spacing-xx-lg">
                <div class="w-100 p-y-spacing-big d-flex justify-content-between">
                  <span class="content-heading-h3 ">Citations and references</span>
                  <div ngbTooltip="Show by" tooltipClass="white-tooltip" class="figma-dropdown">
                    <button [ngbPopover]="popoverFilterCitationTemplate" [autoClose]="true"
                            popoverClass="context-menu-popper" container="body" placement="bottom-right"
                            class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small">
                      <span class="p-x-spacing-xx-s menu-text">{{ selectedCitationFilter }}</span>
                      <i class="fa-regular fa-lg fa-bars-filter ms-2"></i>
                    </button>
                  </div>
                </div>

                <div *ngIf="!searchingCitation && paginationCitation?.total_hits > 0 && !selectedPatent?.general?.obfuscated" class="content-body-medium">
                  <app-citation-patent-table [patents]="citationSearchService?.documents | async" [pagination]="paginationCitation"
                                            isPatentViewerSource="true" [storeService]="storeService">
                  </app-citation-patent-table>
                  <pagination-controls (pageChange)="onCitationsPageChange($event)" autoHide="true"
                                      class="d-flex justify-content-end mt-4" directionLinks="false" id="citations-pagination"
                                      maxSize="10">
                  </pagination-controls>
                </div>
                <div class="full-text content-body-medium" *ngIf="!searchingCitation && paginationCitation?.total_hits === 0">
                  <p>No citations or references found for this document</p>
                </div>
                <ng-container *ngIf="searchingCitation">
                  <div class="d-flex justify-content-center">
                    <img src="assets/images/octimine_blue_spinner.gif">
                  </div>
                </ng-container>
              </div>

              <div id="sec-legal-events" class="nav-section p-t-spacing-x-big">
                <app-legal-events-table [patent]="selectedPatent" [documentInfo]="documentInfo" [isFigma2023]="true"></app-legal-events-table>
              </div>
            </div>
          </div>

          <app-popper #sectionCommentEditor class="dynamic-comment-dialog comments-user-mentioned" [hidden]="!addComment" [showArrow]="false" customClass="comment-popper" [fallbackPlacements]="['top', 'bottom']" [allowedClickClasses]="['comment-avatar']">
            <app-patent-comment-editor *ngIf="addComment" [allTeamUsers]="teamUsers" placeholder="@mention or comment" [allTeamGroups]="teamGroups"
              [focused]="true" (cancelComment)="hidePoppers()" (saveComment)="addSectionComment($event)"
              (mentionSelected)="sectionCommentEditor.blockClosing()" (mentionClosed)="sectionCommentEditor.unblockClosing()">
            </app-patent-comment-editor>
          </app-popper>

          <app-popper #octiAIPopper [hidden]="!showOctiAIPopper" [showArrow]="false" customClass="octi-ai-popper" [fallbackPlacements]="['top', 'bottom']" >
            <app-octi-ai-popper [mouseUpElementId]="mouseUpElementId" [octiAIPopper]="octiAIPopper" (popperClosed)="closeOctiAIPopper()" (octiSubmit)="onSubmitPopper($event)"></app-octi-ai-popper>
          </app-popper>

        </div>
        <div class="btn-got-top" [hidden]="scrollPercent<2" ngbTooltip="Go to the top" tooltipClass="white-tooltip">
          <div class="circle-border {{scrollBorder}}" (click)="scrollTo('sec-title-info')">
            <div class="circle">
              <i class="fa-regular fa-chevron-up fa-lg"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="figma-side-section-container radius-lg h-100" *ngIf="sideSection"
         [ngClass]="{ 'p-spacing-md': sideSection != sideSectionEnum.RATINGS && sideSection != sideSectionEnum.COMMENTS && sideSection != sideSectionEnum.OCTI_AI }">
      <div *ngIf="[sideSectionEnum.RATINGS, sideSectionEnum.COMMENTS].indexOf(sideSection) === -1"
           class="d-flex flex-column h-100">
        <div [class.p-spacing-md]="sideSection == sideSectionEnum.OCTI_AI">
          <app-patent-side-section-header [title]="sideSection"
                                          [tooltip]="getSideSectionTooltip()"
                                          [tooltipClass]="getSideSectionTooltipClass()"
                                          (closeEvent)="onCloseSideSectionClicked()">
            <ng-container sideButtons *ngIf="sideSection == sideSectionEnum.OCTI_AI">
              <span class="button-main-tertiary-grey button-small" (click)="onNewChat()" *ngIf="octiAIHistory?.length>0" ngbTooltip="Opening a new chat will erase your current conversation.">New chat</span>
            </ng-container>
          </app-patent-side-section-header>
        </div>

        <div class="figma-side-section-content w-100 flex-grow-1" [class.m-t-spacing-md]="sideSection != sideSectionEnum.OCTI_AI">
          <ng-container [ngSwitch]="sideSection">
            <app-octi-panel *ngSwitchCase="sideSectionEnum.OCTI_AI"
            [storeService]="storeService" wrapperClass="figma-side-section-content"
            [chatID]="octiChatID" [familyId]="familyID" [publicationNumbers]="!isFamilyView ? activePublicationNumber: null">
            </app-octi-panel>

            <app-patent-highlights-section *ngSwitchCase="sideSectionEnum.HIGHLIGHTS"
                                           [isSavingHighlight]="patentHighlights.isSavingHighlight || isLoadingDocumentHighlights"
                                           (changeHighlightColor)="changeHighlightColor($event)"
                                           (highlightsSorted)="onHighlightsSorted()"
                                           class="w-100 h-100 d-flex flex-column align-items-stretch overflow-auto">
            </app-patent-highlights-section>

            <app-patent-list *ngSwitchCase="sideSectionEnum.LIST" class="w-100 h-100" [patent]="selectedPatent"
                             [storeService]="storeService" [searchService]="searchService"
                             (openDocument)="onCurrentListOpenDocument($event)"
                             (openDocumentNewTab)="onCurrentListOpenNewTab($event)">
            </app-patent-list>

            <app-patent-relevance *ngSwitchCase="sideSectionEnum.RELEVANCE"
                                  [smartHighlights]="getSmartHighlights(selectedPatent)"
                                  [isLoadingRelevanceParts]="isLoadingSmartHighlights"
                                  class="w-100 h-100 d-flex flex-column align-items-stretch overflow-auto">
            </app-patent-relevance>
          </ng-container>
        </div>
      </div>

      <app-patent-comments *ngIf="sideSection == sideSectionEnum.COMMENTS" class="d-flex flex-column h-100" [patent]="selectedPatent"
              [storeService]="storeService" [actionSource]="patentCommentsActionSource" [teamUsers]="teamUsers"
              [teamGroups]="teamGroups" [focusedComment]="focusedComment" [isLoading]="isLoadingDocumentHighlights"
              [tooltip]="getSideSectionTooltip()" [tooltipClass]="getSideSectionTooltipClass()" (closeEvent)="onCloseSideSectionClicked()">
      </app-patent-comments>

      <app-patent-ratings *ngIf="sideSection == sideSectionEnum.RATINGS"
                          class="d-flex flex-column h-100"
                          [patent]="selectedPatent" [storeService]="storeService"
                          (closeEvent)="onCloseSideSectionClicked()">
      </app-patent-ratings>
    </div>
    <div class="figma-gallery-container p-spacing-md radius-lg" *ngIf="hasGallery" data-intercom-target="images"
         [ngClass]="isGalleryCollapsed ? 'collapsed-gallery' : 'expanded-gallery'" ngClass.xs="sx" ngClass.lg="lg">
      <div class="figma-gallery-title content-4 p-spacing-none gap-spacing-x-s content-heading-h4">
        <span class="button-main-tertiary-grey button-square button-small" tooltipClass="white-tooltip" (click)="toggleGalery()" [ngbTooltip]="isGalleryCollapsed ? 'Expand' : 'Collapse'">
          <i class="fa-regular fa-chevrons-{{isGalleryCollapsed?'left': 'right'}}"></i>
        </span>
        <i class="fa-regular fa-image figma-gallery-icon m-l-spacing-x-s"></i>
        <span class="figma-gallery-text  m-l-spacing-x-s">Images</span>
      </div>
      <div class="figma-gallery-box p-spacing-none">
        <div class="figma-gallery-box-item" tooltipClass="white-tooltip tooltip-text-only"
             [ngbTooltip]="isLoadingImage ? 'Waiting for loading the image' : 'Open in full screen'">
          <app-patent-image #galleryBox [publication]="isPublicationView ? patentViewService.activePublicationName : null"
                            [patent]="selectedPatent" [image]="selectedImage.src" [isLoadingImage]="isLoadingImage"
                            [selectedImageIndex]="selectedImageIndex"
                            [showImageSource]="!isGalleryCollapsed" [isSmallIcon]="isGalleryCollapsed"
                            data-intercom-target="Patent image">
          </app-patent-image>
        </div>
      </div>
      <div class="figma-gallery-divider-dashed w-100"></div>
      <div class="figma-gallery-items">
        <div class="figma-gallery-items-wrapper">
          <div class="figma-gallery-item" *ngFor="let img of selectedPatent?.images; let i = index;"
               (click)="loadHighQualityImage(img)"
               [hidden]="patentViewService.activePublicationName && patentViewService.activePublicationName !== img.patentNumber"
               [ngStyle]="{'background-image': 'url(' + img.src + ')'}"
               [ngClass]="{'selected': selectedImageIndex === i}">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="loading-patent d-flex justify-content-center align-items-center" *ngIf="!selectedPatent">
  <app-spinner></app-spinner>
</div>

<ng-template #linkTemplate let-values="values" let-field="field" let-labels="labels">
  <ng-container *ngIf="!values || values?.length === 0" [ngTemplateOutlet]="notAvailable"></ng-container>
  <ng-container *ngFor="let v of values; let i = index; let last = last;">
    <a *ngIf="hasLinksToBooleanSearch" routerLink="/boolean" target="_blank"
       class="button-main-link border-0 p-spacing-none"
       [queryParams]="{field: field.toUpperCase(), value: v, label: labels ? labels[i] : ''}"
       [innerHtml]="labels ? labels[i] : v"></a>
    <a *ngIf="!hasLinksToBooleanSearch" class="button-main-link no-link border-0 p-spacing-none" [innerHtml]="labels ? labels[i] : v"></a>
    <span class="button-main-link border-0 p-spacing-none" *ngIf="!last">, </span>
  </ng-container>
</ng-template>

<div id="searchBox" [hidden]="!isSearchBoxOpen" class="search-box d-flex p-spacing-md gap-spacing-sm radius-big">
  <div class="input-frame">
    <input id="inputSearchTerm" placeholder="Find in patent" class="form-control" [(ngModel)]="searchTerm"
      (ngModelChange)="highlightSearchTerm()" (keyup.enter)="nextMatch()">
    <span class="counters" [hidden]="!matches.length">{{currentIndex + 1}}/{{matches.length}}</span>
  </div>
  <div class="buttons-search d-flex">
    <div class="buttons" (click)="previousMatch()" ngbTooltip="Previous" tooltipClass="white-tooltip">
      <span><i class="fa-regular fa-chevron-up"></i></span>
    </div>
    <div class="buttons" (click)="nextMatch()" ngbTooltip="Next" tooltipClass="white-tooltip">
      <span><i class="fa-regular fa-chevron-down"></i></span>
    </div>
    <div class="buttons" [ngClass]="{'active': isWholeWord}" (click)="isWholeWord = !isWholeWord; highlightSearchTerm()"
      ngbTooltip="Match whole word" tooltipClass="white-tooltip">
      <span><i class="fa-regular fa-input-text"></i></span>
    </div>
    <div class="buttons" (click)="closeSearchBox()" ngbTooltip="Close" tooltipClass="white-tooltip">
      <span><i class="fa-regular fa-xmark"></i></span>
    </div>
  </div>
</div>

<app-patent-highlights #patentHighlights
  [selectedPatent]="selectedPatent" [addComment]="addComment" [isOverTextArea]="isOverTextArea"
  (save)="onSaveHighlight()"
  (closePoppers)="highlightClosePoppers()"
  (selectPopperAction)="setPopperAction($event)"
  (openSideSection)="toggleSideSection(sideSectionEnum.HIGHLIGHTS)">
</app-patent-highlights>

<app-popper #octiAIPopper [hidden]="!showOctiAIPopper" [showArrow]="false" customClass="octi-ai-popper" >
  <app-octi-ai-popper [mouseUpElementId]="mouseUpElementId" [octiAIPopper]="octiAIPopper" (popperClosed)="closeOctiAIPopper()" (octiSubmit)="onSubmitPopper($event)"></app-octi-ai-popper>
</app-popper>

<ng-template #popoverFilterCitationTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-md">Show</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check content-body-medium p-r-spacing-s"
       *ngFor="let filter of filterCitationOptions"
       (click)="filterCitationList(filter)"
       [class.active]="filter === selectedCitationFilter">
    <span class="p-r-spacing-xxx-big">{{ filter }} </span>
  </div>
</ng-template>

<ng-template #notAvailableMessage>
  <span class="ignore-search-mark"><i class="fa-regular fa-circle-info"></i> Text not available. This section of the document was not yet made available by the patent office.</span>
</ng-template>

<ng-template #notAvailable>
  <span class="content-label-large">
    <i class="fa-regular fa-circle-info"></i> Not available
  </span>
</ng-template>
