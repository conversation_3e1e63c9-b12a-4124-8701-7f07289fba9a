import { Component, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { AnnotationService, AnnotationStoreService, DocumentAnnotation, Label, Patent, TeamUser, UserService } from '@core';
import { PatentViewHighlightService } from '@core/services/patent-view-highlight/patent-view-highlight.service';
import { PopperComponent } from '@shared/components/popper/popper.component';
import {
  ColorsPopperSourceEnum,
  InlineHighlightFilterOptionEnum,
  PatentHighlightActionEnum,
  SideSectionEnum
} from '@patent/types';
import { ModalDialogComponent } from '@shared/components/modal-dialog/modal-dialog.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subscription, finalize, take } from 'rxjs';

@Component({
  selector: 'app-patent-highlights',
  templateUrl: './patent-highlights.component.html',
  styleUrls: ['./patent-highlights.component.scss']
})
export class PatentHighlightsComponent implements OnInit, OnDestroy {

  @Input() selectedPatent: Patent;
  @Input() addComment: boolean;
  @Input() isOverTextArea = false;

  @Output() save = new EventEmitter<void>();
  @Output() closePoppers = new EventEmitter<void>();
  @Output() selectPopperAction = new EventEmitter<{action: PatentHighlightActionEnum, event: any}>();
  @Output() openSideSection = new EventEmitter<void>();

  @ViewChild('highlightPopper') highlightPopper: PopperComponent;
  @ViewChild('colorsPopper') colorsPopper: PopperComponent;
  @ViewChild('inlineFilterPopper') inlineFilterPopper: PopperComponent;

  mouseUpElementId: string = null;
  colorCursor = false;
  eraserCursor = false;
  defaultHighlightColor = '#F8FFA8';
  patentHighlightAction: PatentHighlightActionEnum = null;
  sideSectionEnum = SideSectionEnum;
  patentHighlightActionEnum = PatentHighlightActionEnum;
  isSavingHighlight = false;
  selectedHighlight: DocumentAnnotation;
  hiddenPopper = false;
  cursorPosition = {x: 0, y: 0};
  ignoreNextScroll: boolean = false;

  filterOptions: InlineHighlightFilterOptionEnum[] = [
    InlineHighlightFilterOptionEnum.SHOW_ALL,
    InlineHighlightFilterOptionEnum.MY_HIGHLIGHTS,
    InlineHighlightFilterOptionEnum.HIDE_ALL
  ];

  private colorsPopperSource: ColorsPopperSourceEnum;
  private subscriptions = new Subscription();

  constructor(
    public userService: UserService,
    private patentViewHighlightService: PatentViewHighlightService,
    private ngbModal: NgbModal,
    private annotationService: AnnotationService,
    private annotationStoreService: AnnotationStoreService

  ) { }

  ngOnInit(): void {
    const highlightRemoved$ = this.patentViewHighlightService.removedHighlightEvent$.subscribe({
      next: (idHighlight) => {
        this.deleteHighlight(idHighlight);
      }
    });
    this.subscriptions.add(highlightRemoved$);

    const savedHighlight$ = this.patentViewHighlightService.savedHighlightEvent$.subscribe({
      next: (highlight) => {
        this.saveHighlight(highlight.savedHighlight, highlight.selectedElements);
      }
    });
    this.subscriptions.add(savedHighlight$);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  get isColorTextSelectionEnabled(): boolean {
    return ((this.colorCursor && this.selectedColor) || this.eraserCursor) && this.colorsPopperSource === ColorsPopperSourceEnum.TOP_MENU;
  }

  get selectedColor(): string {
    return this.patentViewHighlightService.selectedColor;
  }
  set selectedColor(value: string) {
    this.patentViewHighlightService.selectedColor = value;
  }

  get isHighlightChanging(): boolean {
    return this.colorsPopperSource === ColorsPopperSourceEnum.HIGHLIGHT_LIST;
  }

  get hasHighlights(): boolean {
    return this.highlightList?.length > 0;
  }

  get highlightList(): DocumentAnnotation[] {
    return this.patentViewHighlightService.highlightList;
  }

  set highlightList(value: DocumentAnnotation[]) {
    this.patentViewHighlightService.highlightList = value;
  }

  get originalHighlights(): DocumentAnnotation[] {
    return this.patentViewHighlightService.originalHighlights;
  }

  set originalHighlights(value: DocumentAnnotation[]) {
    this.patentViewHighlightService.originalHighlights = value;
  }

  get mouseDownElementId(): string {
    return this.patentViewHighlightService.mouseDownElementId;
  }
  set mouseDownElementId(value: string) {
    this.patentViewHighlightService.mouseDownElementId = value;
  }

  get isCustomCursorEnabled(): boolean {
    return this.colorCursor || this.eraserCursor;
  }

  get popperHeight(): number {
    return this.highlightPopper.popperInstance.nativeElement.offsetHeight;
  }

  get isColorSelectedFromPopperMenu(): boolean {
    return this.selectedColor && this.colorsPopperSource === ColorsPopperSourceEnum.POPPER_MENU;
  }

  get highlights(): Label[] {
    return this.patentViewHighlightService.highlights;
  }

  set highlights(value: Label[]) {
    this.patentViewHighlightService.highlights = value;
  }

  get isTopMenuColorsPopperOpen(): boolean {
    return this.colorsPopperSource === ColorsPopperSourceEnum.TOP_MENU;
  }

  get currentInlineFilter(): InlineHighlightFilterOptionEnum {
    return this.patentViewHighlightService.currentInlineFilter;
  }

  set currentInlineFilter(value: InlineHighlightFilterOptionEnum) {
    this.patentViewHighlightService.currentInlineFilter = value;
  }

  onMouseDown(event: MouseEvent) {
    const isLeftMouse = event.button === 0;
    if (isLeftMouse && !this.popperClicked(event)) {
      if (this.isColorSelectedFromPopperMenu) {
        this.applyHighlight();
      } else if (this.isColorTextSelectionEnabled) {
        this.colorsPopper.hide();
      } else {
        if(!this.referenceTextClicked(event)){
          this.patentViewHighlightService.clearCurrentHighlight();
          this.annotationService.clearHighlightedExplainingText();
        }
        this.hidePoppers();
        this.patentHighlightAction = null;
      }

      this.patentViewHighlightService.mouseDownElementId = this.patentViewHighlightService.getElementId(event);
    }
  }

  @HostListener('document:mouseup', ['$event'])
  onMouseUp(event: MouseEvent) {
    const isLeftMouse = event.button === 0;
    if (isLeftMouse && !this.popperClicked(event)) {
      if (!this.isColorTextSelectionEnabled) {
        this.mouseUpElementId = this.patentViewHighlightService.getElementId(event);
      }
      this.openHighlightPopper(event);
      this.annotationService.clearHighlightedExplainingText();
    }
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    this.customizeCursor(event);
  }

  @HostListener('window:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      this.patentViewHighlightService.clearCurrentHighlight();
      this.hidePoppers();
      this.customizeCursor();
      document.documentElement.style.removeProperty('--selection-bg');
    }
  }

  getHighlights() {
    const getLabels$ = this.annotationService.getLabels({is_highlight: 1})
    .pipe(take(1))
    .subscribe({
      next: ({labels}) => {
        this.highlights = labels.sort((a, b) => a.id - b.id).map(label => {
          label.color = '#' + label.color;
          label.name = label.name.replace('Highlight', '');
          return label;
        });
      }
    });
    this.subscriptions.add(getLabels$);
  }

  openHighlightPopper(event: MouseEvent) {
    if (!this.userService.canUserAnnotate()) {
      return;
    }

    const isLeftMouse = event.button === 0;
    if (isLeftMouse) {
      const highlightSelection = this.patentViewHighlightService.setHighlightSelection(event);
      if (highlightSelection) {
        if (this.isColorTextSelectionEnabled) {
          if (!this.eraserCursor) {
            this.applyHighlight();
            window.getSelection().removeAllRanges();
          } else {
            this.removeHighlightsBySelection();
          }
          return;
        }

        const selectedHighlightColor = this.patentViewHighlightService.getSelectedHighlightColor();
        this.defaultHighlightColor = selectedHighlightColor.selectedColor || selectedHighlightColor.defaultColor;
        this.selectedColor = selectedHighlightColor.selectedColor;

        this.highlightPopper.show(highlightSelection.highlightNode);
      }
    }
  }

  popperClicked(event: MouseEvent) {
    for (const tag of event.composedPath()) {
      const element = tag as HTMLElement;
      if (element.classList?.contains('popper-instance')) {
        return true;
      }
    }
    return false;
  }

  referenceTextClicked(event: MouseEvent) {
    for (const tag of event.composedPath()) {
      const element = tag as HTMLElement;
      if (element.classList?.contains('reference-text-container')) {
        return true;
      }
    }
    return false;
  }

  openColorsPopper(event: MouseEvent, source: ColorsPopperSourceEnum) {
    this.colorsPopperSource = source;
    this.colorsPopper.placement = this.isHighlightChanging ? 'bottom-end' : 'bottom-start';
    this.colorsPopper.offset = 10;
    this.selectedColor = this.defaultHighlightColor;

    const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    if (isFirefox && source === ColorsPopperSourceEnum.POPPER_MENU) {
      this.ignoreNextScroll = true;
      setTimeout(() => {
        this.ignoreNextScroll = false;
      }, 300);
    }

    this.colorsPopper.show((event.currentTarget as HTMLElement));
    if (source === ColorsPopperSourceEnum.TOP_MENU) {
      this.colorCursor = true;
    }
  }

  onScrollColorMenu(event) {
    if (this.ignoreNextScroll) {
      return;
    }

    if (this.highlightPopper.isOpen && this.colorsPopper.isOpen) {
      this.colorsPopper.hide();
      this.colorsPopperSource = null;
      this.selectedColor = '';
    }
    this.showHidePopperOnScreenTop(event);
  }

  private showHidePopperOnScreenTop(event) {
    const selection = window.getSelection();
    if (!selection || selection.type === 'None') {
      return;
    }
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const containerRect = event.target.getBoundingClientRect();
    this.hiddenPopper = rect.top - this.popperHeight - 10 < containerRect.top;
  }

  removeHighlights() {
    if (this.disableEraserButton()) {
      return;
    }

    if (this.colorsPopperSource === ColorsPopperSourceEnum.HIGHLIGHT_LIST) {
      this.removeHighlightsByList();
      return;
    }

    this.colorCursor = false;
    if (!this.highlightPopper.isOpen) {
      this.eraserCursor = !this.eraserCursor;
      this.selectedColor = '';
      return;
    }

    this.removeHighlightsBySelection();
  }

  private removeHighlightsByList() {
    if (!this.hasHighlights) {
      return;
    }

    const modalRef = this.ngbModal.open(ModalDialogComponent, {size: 'md', backdrop: 'static', 'centered': true});
    modalRef.componentInstance.options = {
      title: 'Remove highlights',
      question: 'Would you like to remove this highlight?',
      confirmButton: 'Remove',
      cancelButton: 'Cancel'
    };
    modalRef.result.then((result) => {
      if (result) {
        this.patentViewHighlightService.removeHighlightsById(this.selectedHighlight.id, true);
        this.hidePoppers();
      }
    }, () => {});
  }

  private removeHighlightsBySelection() {
    if (this.patentViewHighlightService.removeHighlightsBySelection()) {
      this.hidePoppers();
      window.getSelection()?.removeAllRanges();
      if (this.highlightList.length === 0) {
        this.eraserCursor = false;
      }
    }
  }

  private clearSelectedColor() {
    this.selectedColor = '';
    this.isOverTextArea = false;
    this.colorCursor = false;
    this.eraserCursor = false;
  }

  private saveHighlight( highlight: DocumentAnnotation, selectedElements) {
    if (highlight.id) {
      this.updateSavedHighlight(highlight);
      return;
    }
    const payload = {
      entries: [
        {
          field: this.mouseDownElementId.replace('-text', ''),
          text: highlight.text.replace(/\s/g, ' '),
          start_pos: highlight.start_pos,
          end_pos: highlight.end_pos,
          label_id: highlight.label_id
        } as DocumentAnnotation
      ]
    }
    this.isSavingHighlight = true;
    const saveDocumentComment$ = this.annotationService.saveDocumentLabel(payload, parseInt(this.selectedPatent.general.docdb_family_id))
      .pipe(
        finalize( () => {
          this.isSavingHighlight = false;
        })
      )
      .subscribe({
        next: ({data}) => {
          const label = data[0];
          selectedElements.forEach((hl: HTMLElement) => {
            hl.dataset.id = 'hl-' + label.id;
          });
          highlight.id = label.id;
          label.user = this.getUserHighlight();
          this.originalHighlights.push(label);
          this.filterAndSortHighlights();
          this.save.emit();
          this.clearHighlightData();
        },
        error: (error) => {
          console.error(error);
        }
      });
    this.subscriptions.add(saveDocumentComment$);
  }

  private getUserHighlight() {
    const user = {} as TeamUser;
    user.id = this.userService.getUser().profile.id;
    user.first_name = this.userService.getUser().profile.first_name;
    user.last_name = this.userService.getUser().profile.last_name;
    user['name'] = this.userService.getUser().profile.first_name + ' ' + this.userService.getUser().profile.last_name;
    user.email = this.userService.getUser().profile.email;
    return user;
  }

  private updateSavedHighlight(highlight: DocumentAnnotation) {
    const payload: DocumentAnnotation = {
      field: this.mouseDownElementId?.replace('-text', '') || highlight.field,
      text: highlight.text.replace(/\s/g, ' '),
      start_pos: highlight.start_pos,
      end_pos: highlight.end_pos,
      label_id: highlight.label_id
    }
    this.isSavingHighlight = true;
    const updateDocumentComment$ = this.annotationService.updateDocumentLabel(payload, parseInt(this.selectedPatent.general.docdb_family_id), highlight.id)
      .pipe(
        finalize( () => this.isSavingHighlight = false)
      )
      .subscribe({
        next: ({data}) => {
          this.save.emit();
        },
        error: (error) => {
          console.error(error);
        }
      });
    this.subscriptions.add(updateDocumentComment$);
  }

  private deleteHighlight(id: number) {
    this.isSavingHighlight = true;
    const deleteDocumentComment$ = this.annotationService.deleteDocumentLabel(parseInt(this.selectedPatent.general.docdb_family_id), id)
    .pipe(
      finalize(() => this.isSavingHighlight = false)
    )
    .subscribe({
      next: () => {
        this.originalHighlights = this.originalHighlights.filter(h => h.id !== id);
        this.filterAndSortHighlights();
        this.selectedHighlight = null;
        if (this.highlightList.length === 0) {
          this.eraserCursor = false;
        }
        this.annotationStoreService.removeAnnotation = 'labels';
      },
      error: (err) => {
        console.error(err);
      }
    });
    this.subscriptions.add(deleteDocumentComment$);
  }

  hidePoppers() {
    this.hiddenPopper = false;
    this.highlightPopper.hide();
    this.colorsPopper.hide();
    this.colorsPopperSource = null;
    this.inlineFilterPopper.hide();
    this.clearSelectedColor();
    this.closePoppers.emit();
  }

  toggleColor(color?: Label) {
    if (this.selectedColor && this.selectedColor == color.color) {
      return;
    }

    this.colorCursor = false;
    this.eraserCursor = false;

    this.selectedColor = this.selectedColor == color.color ? '' : color.color;
    this.defaultHighlightColor = this.selectedColor;
    if (this.selectedColor && this.isHighlightChanging) {
      this.selectedHighlight.label_id = color.id;
      this.selectedHighlight.label = {...color, color: color.color.replace('#', '')};
      if (this.patentViewHighlightService.updateHighlightColor(this.selectedHighlight.id)) {
        const highlights = this.patentViewHighlightService.getHighlightsByDataId(this.selectedHighlight.id);
        this.saveHighlight(this.selectedHighlight, highlights);
      }
      this.selectedColor = '';
      this.defaultHighlightColor = '';
      this.colorsPopper.hide();
      this.colorsPopperSource = null;
      return;
    }

    if (this.selectedColor && this.colorsPopperSource === ColorsPopperSourceEnum.TOP_MENU) {
      this.colorCursor = true;
      this.customizeCursor();
    }
  }

  applyHighlight() {
    this.patentViewHighlightService.applyHighlight();
    if (this.highlightPopper.isOpen) {
      this.hidePoppers();
    }
  }

  private customizeCursor(event?: MouseEvent) {
    if (this.isCustomCursorEnabled && event) {
      this.cursorPosition.x = event.clientX;
      this.cursorPosition.y = event.clientY;
      document.documentElement.style.setProperty('--cursor', 'none');
    } else {
      document.documentElement.style.removeProperty('--cursor');
      this.cursorPosition = {x: 0, y: 0};
    }
  }

  changeHighlightColor(event) {
    this.selectedHighlight = event.highlight;
    this.defaultHighlightColor = '#' + event.highlight.label.color;
    this.colorCursor = false;
    this.openColorsPopper(event.event, ColorsPopperSourceEnum.HIGHLIGHT_LIST);
  }

  disableEraserButton(): boolean {
    if (this.colorsPopperSource === ColorsPopperSourceEnum.TOP_MENU) {
      return !this.highlightList.length;
    }
    if (this.colorsPopperSource === ColorsPopperSourceEnum.HIGHLIGHT_LIST) {
      return false;
    }
    return !this.patentViewHighlightService.isThereHighlightsInNewSelection();
  }

  updateHighlightPopper() {
    if (this.isTopMenuColorsPopperOpen) {
      this.colorsPopper.hide();
    } else{
      if (this.highlightPopper.isOpen) {
        setTimeout(() => {
          this.highlightPopper.popperInstanceObject.update();
        }, 100);
      }
      if (this.colorsPopper.isOpen) {
        setTimeout(() => {
          this.colorsPopper.popperInstanceObject.update();
        }, 100);
      }
    }

    if (this.isColorTextSelectionEnabled) {
      this.colorCursor = false;
      this.eraserCursor = false;
    }
  }

  setPopperAction(action: PatentHighlightActionEnum, event = null) {
    this.selectPopperAction.emit({action: action, event: event});
  }

  clearHighlightData() {
    this.patentHighlightAction = null;
    this.mouseDownElementId = null;
    this.patentViewHighlightService.clearCurrentHighlight();
    this.hidePoppers();
  }

  onOpenSideSection() {
    this.openSideSection.emit()
  }

  onInlineFilterOptionClicked(op: InlineHighlightFilterOptionEnum, event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.hidePoppers();
    this.currentInlineFilter = op;
    this.patentViewHighlightService.clearCurrentHighlight();
    this.filterAndSortHighlights();
    this.patentViewHighlightService.refreshHighlights();
  }

  isFilterOptionDisabled(op: InlineHighlightFilterOptionEnum): boolean {
    if (op !== InlineHighlightFilterOptionEnum.MY_HIGHLIGHTS) {
      return false;
    }

    return !this.patentViewHighlightService.originalHighlights.some(hl => hl.user_id === this.userService.userId);
  }

  private filterAndSortHighlights() {
    console.log(this.originalHighlights.length, this.highlightList.length, '+++++++++++++');
    this.patentViewHighlightService.filterHighlights();
    this.patentViewHighlightService.sortHighlights();
    console.log(this.originalHighlights.length, this.highlightList.length, '-------------');
  }
}
