<app-popper #highlightPopper [hidden]="hiddenPopper">
    <div [hidden]="addComment" class="highlight-popper d-flex align-items-center justify-content-start" (mousedown)="$event.preventDefault(); $event.stopPropagation()">
      <div *ngIf="userService.hasOctiAiFeature()"
           class="button-main-tertiary button-small p-x-spacing-md button-octi-ai"
           [ngClass]="{'active': patentHighlightAction === patentHighlightActionEnum.OCTI_AI}"
           (click)="setPopperAction(patentHighlightActionEnum.OCTI_AI)"
           ngbTooltip="Get answers, understand patents, and more."
           tooltipClass="white-tooltip" container="body">
        <i class="fa-light octi-icon"></i><span class="p-x-spacing-x-s content-label-small">Octi AI</span>
      </div>
      <div #btnHighlight class="button-main-tertiary-grey button-small p-x-spacing-md"
           [ngClass]="{'active': patentHighlightAction === patentHighlightActionEnum.HIGHLIGHT}"
           (click)="setPopperAction(patentHighlightActionEnum.HIGHLIGHT, $event)">
        <span class="popper-color-highlight-icon" [style.background-color]="selectedColor">
          <i class="fa-light fa-highlighter"></i>
        </span>
        <span class="p-x-spacing-x-s content-label-small">Highlight</span>
      </div>
      <div class="button-main-tertiary-grey button-small p-x-spacing-md"
           [ngClass]="{'active': patentHighlightAction === patentHighlightActionEnum.COMMENTS}"
           (click)="setPopperAction(patentHighlightActionEnum.COMMENTS, $event)">
        <i class="fa-light fa-message"></i><span class="p-x-spacing-x-s content-label-small">Comments</span>
      </div>
    </div>
</app-popper>

<app-popper #colorsPopper placement="bottom-start" [showArrow]="false">
    <div class="colors-popper inline-modal-container">
      <div class="inline-modal-block">
        <div class="menu-colors d-flex gap-spacing-sm">
            <a class="color-option" *ngFor="let color of highlights" [style.background-color]="color.color" [ngClass]="{'active': selectedColor == color.color}"
                (click)="toggleColor(color)" [ngbTooltip]="color.name" tooltipClass="white-tooltip">
                <span *ngIf="selectedColor == color.color"><i class="fa-solid fa-check"></i></span>
            </a>
            <a class="color-option none d-flex justify-content-center align-items-center button-main-tertiary-grey button-square"
               [ngClass]="{'disabled': disableEraserButton()}" (click)="removeHighlights()"
               [ngbTooltip]="isHighlightChanging ? 'Remove' : 'Erase'" tooltipClass="white-tooltip">
                <i class="fa-light fa-eraser"></i>
            </a>
        </div>
      </div>

      <ng-container *ngIf="!isHighlightChanging">
        <div class="popover-divider"></div>

        <div class="inline-modal-block p-spacing-sm">
          <div class="figma-dropdown-item figma-dropdown-item-hover button-small w-100 content-color-primary d-flex justify-content-between align-items-center"
               (mouseover)="inlineFilterPopper.show($event.currentTarget)">
            <div class="content-body-medium">Show all</div>
            <i class="fa-regular fa-chevron-right"></i>
          </div>
        </div>
      </ng-container>

      <ng-container *ngIf="!isHighlightChanging">
        <div class="popover-divider"></div>

        <div class="inline-modal-block p-spacing-sm">
          <a class="button-main-tertiary-grey button-small gap-spacing-sm w-100" (click)="onOpenSideSection()">
            <i class="fa-regular fa-arrow-up-right-from-square"></i>
            <div class="content-label-small open-all-highlights-title">Open all highlights</div>
          </a>
        </div>

        <div class="inline-modal-block p-spacing-sm content-body-xsmall gap-spacing-xx-s flex-row align-items-start"
             *ngIf="isTopMenuColorsPopperOpen">
            <i class="fa-regular fa-circle-info p-t-spacing-xx-s"></i>
            <span>Click ESC on your keyboard to cancel the highlights</span>
        </div>
      </ng-container>
    </div>
</app-popper>

<app-popper #inlineFilterPopper placement="right-start" [showArrow]="false" [offset]="10"
            customClass="inline-filter-popper" (mouseleave)="inlineFilterPopper.hide()">
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check"
       *ngFor="let op of filterOptions"
       [class.active]="currentInlineFilter === op"
       (click)="onInlineFilterOptionClicked(op, $event); inlineFilterPopper.hide();">
    {{ op }}
  </div>
</app-popper>

<div style="position: fixed; pointer-events: none;" [style.top.px]="cursorPosition.y" [style.left.px]="cursorPosition.x" *ngIf="isOverTextArea && isCustomCursorEnabled">
    <div class="custom-cursor">
      <div class="custom-cursor-point">
        <img src="assets/images/figma/cursor-highlights.svg">
      </div>
      <span class="custom-cursor-icon" [style.background-color]="selectedColor">
        <i class="fa-solid fa-highlighter-line custo-cursor-icon" *ngIf="colorCursor"></i>
        <i class="fa-solid fa-eraser custo-cursor-icon" *ngIf="eraserCursor"></i>
      </span>
    </div>
</div>
