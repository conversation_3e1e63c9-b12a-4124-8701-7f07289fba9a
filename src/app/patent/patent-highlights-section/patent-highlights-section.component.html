<ng-container *ngIf="!isSavingHighlight else loadingHighlights">
  <div class="w-100 d-flex justify-content-between highlights-menu m-b-spacing-x-big">
    <div></div>
    <div class="d-flex gap-spacing-xx-s">
      <div class="figma-dropdown comment-filter-by">
        <button [ngbPopover]="popoverFilterHighlightsTemplate"
                #filterPopover="ngbPopover"
                [popoverContext]="{options: patentViewHighlightService.highlightsFilterOptions, showHeader: true}"
                [autoClose]="'outside'" popoverClass="inline-menu-popper"
                container="body" placement="bottom-right"
                class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small"
                [class.content-color-secondary]="patentViewHighlightService.isHighlightsFilterByDefault()"
                [class.content-color-primary]="!patentViewHighlightService.isHighlightsFilterByDefault()"
                (hidden)="onHighlightsFilterPopoverClosed()"
                [ngbTooltip]="getSelectedHighlightsFilterOptionsTooltip()"
                tooltipClass="white-tooltip text-left">
          <i class="fa-regular fa-lg fa-bars-filter"></i>
          {{ patentViewHighlightService.parentOfSelectedFilterOption.item }}
          <span
            *ngIf="patentViewHighlightService.parentOfSelectedFilterOption.multiple && patentViewHighlightService.highlightsFilterBy.length > 0"
            class="badge-circle content-label-small content-color-reversed-grey figma-bg-reversed">
            {{ patentViewHighlightService.highlightsFilterBy.length }}
          </span>
        </button>
      </div>
      <div class="figma-dropdown comment-sort-by">
        <button [ngbPopover]="popoverSortHighlightsTemplate" [autoClose]="true" popoverClass="context-menu-popper"
                container="body" placement="bottom-right"
                class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small button-square"
                [class.content-color-secondary]="patentViewHighlightService.isHighlightsSortByDefault()"
                [class.content-color-primary]="!patentViewHighlightService.isHighlightsSortByDefault()">
          <i class="fa-regular fa-lg fa-arrow-up-arrow-down"></i>
        </button>
      </div>
    </div>
  </div>

  <div class="w-100 h-100 overflow-auto"
       [ngClass]="{'p-r-spacing-xxx-s': highlightList?.length}">
      <ng-container *ngIf="highlightList?.length else noHighlights">
        <div class="highlight-section mb-4" *ngFor="let highlight of highlightList">
          <div class="highlight-section-head d-flex justify-content-between align-items-center mb-2">
            <app-user-avatar [user]="highlight.user" [hasSubTitle]="false" [showTooltip]="false" size="small"></app-user-avatar>
            <div class="flex-grow-1 ms-2">
              <div class="content-heading-h5">{{ getNameOfHighlightAuthor(highlight) }}</div>
              <div class="content-heading-h7 content-color-tertiary">{{ highlight.created_at | timeReadable }}</div>
            </div>
            <div class="button-main-tertiary-grey button-square button-small cursor-pointer"
                 ngbTooltip="Colour" tooltipClass="white-tooltip" (click)="onChangeHighlightColor($event, highlight)">
              <div class="highlight-icon" [style.background-color]="'#' + highlight.label.color">
                <i class="fa-regular fa-highlighter fa-1x"></i>
              </div>
            </div>
          </div>
          <div class="highlight-section-body d-flex justify-content-between">
            <div class="highlight-section-bar"></div>
            <div class="highlight-section-text content-quotes-02 content-color-tertiary flex-fill cursor-pointer ellipsis-text-3" (click)="goToHighlight(highlight)">
              {{ highlight.text }}
            </div>
          </div>
        </div>
      </ng-container>
      <ng-template #noHighlights>
        <div class="no-highlights text-center">
          <div class="no-highlights-icon p-4">
            <span>
              <i class="fa-light fa-highlighter"></i>
            </span>
          </div>
        </div>
        <div class="text-center pt-4 px-3">
          <span class="no-highlights-text content-body-medium py-2">
            Select the text you want to highlight. All highlighted text in this patent will appear here.
          </span>
        </div>
      </ng-template>
  </div>

  <ng-template #popoverFilterHighlightsTemplate let-options="options" let-showHeader="showHeader">
    <div *ngIf="showHeader"
         class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-none p-x-spacing-md p-t-spacing-md p-b-spacing-xx-s">
      Filter by
    </div>
    <div class="d-flex flex-column gap-spacing-xx-s highlights-filter-content"
         appScrollbarDetector scrollbarCss="has-scrollbar">
      <ng-container *ngFor="let f of options; let first=first; let last=last;">
        <div [ngbTooltip]="f.disabledMessage" tooltipClass="white-tooltip">
          <div (click)="filterHighlights(f, filterPopover, childFilterPopover)"
               (mouseenter)="onMouseEnterFilterOption(f, childFilterPopover)"
               [class.m-t-spacing-sm]="first" [class.m-b-spacing-sm]="last"
               [class.active]="f.selected || patentViewHighlightService.hasSelectedHighlightsFilterChildren(f)"
               [class.disabled]="(f.hasChildren && !f.children?.length) || f.isDisabled"
               [class.figma-dropdown-item-check]="f.isToggle"
               class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-default-bg content-body-medium m-x-spacing-sm m-b-spacing-none p-x-spacing-md p-y-spacing-sm">
            <div [ngbPopover]="f.hasChildren && f.children?.length ? popoverFilterHighlightsTemplate : null"
                 #childFilterPopover="ngbPopover"
                 [popoverContext]="{options: f.children, showHeader: false}"
                 [autoClose]="false" [placement]="['left-top']"
                 [popoverClass]="'inline-menu-popper ' + (f.hasChildren ? 'm-r-spacing-md' : '')"
                 triggers="manual" container="body"
                 class="d-flex justify-content-between align-items-center gap-spacing-sm w-100">
              @if (f.isCheckbox) {
                <label [class.active]="f.selected" class="figma-checkbox align-items-center">
                  <input type="checkbox" [checked]="f.selected"/>
                  @switch (f.itemType) {
                    @case (treeFilterItemType.TEAM_USER) {
                      <app-user-avatar [user]="f.item" [hasSubTitle]="true" size="xsmall" subtitleMaxWidth="10.625rem"
                                       class="d-block" [showYouSuffix]="true"
                                       [showTooltip]="true" [showUserTitleOnTooltip]="false"
                                       [showUserEmailOnTooltip]="true"
                                       (click)="$event.preventDefault(); $event.stopImmediatePropagation(); filterHighlights(f, filterPopover, childFilterPopover)">
                      </app-user-avatar>
                    }
                    @default {
                      <span>{{ f.item }}</span>
                    }
                  }
                </label>
              } @else {
                <div class="p-r-spacing-xxx-big">
                  @switch (f.itemType) {
                    @case (treeFilterItemType.TEAM_USER) {
                      <app-user-avatar [user]="f.item" [hasSubTitle]="true" size="xsmall" subtitleMaxWidth="10.625rem"
                                       class="d-block"
                                       [showTooltip]="true" [showUserTitleOnTooltip]="false"
                                       [showUserEmailOnTooltip]="true">
                      </app-user-avatar>
                    }
                    @default {
                      {{ f.item }}
                    }
                  }
                </div>
              }
              <i *ngIf="f.hasChildren" class="fa-regular fa-chevron-right p-spacing-s fa-fw fa-1x"></i>
            </div>
          </div>
        </div>
        <div *ngIf="f.showDivider && options.length > 1" class="figma-dropdown-item-divider"></div>
      </ng-container>
    </div>
  </ng-template>

  <ng-template #popoverSortHighlightsTemplate>
    <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-md">Sort by</div>
    <div class="figma-dropdown-item figma-dropdown-item-hover p-r-spacing-s figma-dropdown-item-check content-body-medium"
         *ngFor="let op of sortOptions"
         (click)="onSortHighlightsClicked(op)"
         [class.active]="op === currentSort">
      <span class="p-r-spacing-xxx-big">{{ op }} </span>
    </div>
  </ng-template>
</ng-container>

<ng-template #loadingHighlights>
  <div class="w-100 h-100 d-flex flex-column align-items-stretch highlights-loading-template">
    <div class="d-flex justify-content-between highlights-loading-top"></div>
    <div class="highlights-loading-main-content flex-grow-1 d-flex align-items-center justify-content-center">
      <div class="content-heading-h6 content-color-secondary">Highlights are loading...</div>
    </div>
  </div>
</ng-template>
