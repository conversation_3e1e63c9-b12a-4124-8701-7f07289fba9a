@import "scss/figma2023/variables";

@mixin template-box-style() {
  background: $colour-grey-200;
  border-radius: $radius-sm;
}

.no-highlights {
  margin-top: $spacing-system-spacing-xxx-big;

  &-icon {
    border-radius: 6.25rem;
    border: $spacing-system-spacing-md solid $colour-blue-brand-100;
    background-color: $colour-blue-brand-200;
    display: inline-flex;
    align-items: center;
    gap: .625rem;

    span {
      display: flex;
      width: 3.75rem;
      height: 3.75rem;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      i {
        color: $colour-blue-brand-500;
        text-align: center;
        font-size: $spacing-system-spacing-xx-lg;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
      }
    }
  }

  &-text {
    color: $colours-content-content-tertiary;
  }
}

.highlight-icon {
  border-radius: $radius-xs;
  width: $spacing-system-spacing-x-big;
  height: $spacing-system-spacing-x-big;
  display: flex;
  align-items: center;
  justify-content: center;
}

.highlights-loading-template {
  .highlights-loading-top {
    height: $spacing-system-spacing-x-lg;
    @include template-box-style();
  }

  .highlights-loading-main-content {
    margin-top: $spacing-system-spacing-big;
    @include template-box-style();
  }
}

.highlight-section-text {
  border-left: 2px solid $colour-global-dennemeyer-orange;
  padding-left: $spacing-system-spacing-sm;
}

:host {
  .badge-circle {
    height: 1.25rem;
    width: 1.25rem;
    line-height: 1.125rem;
    padding: 0 !important;
  }
}
