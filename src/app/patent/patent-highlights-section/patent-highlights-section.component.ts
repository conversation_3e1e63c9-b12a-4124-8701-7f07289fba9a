import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DocumentAnnotation, PatentViewService, TeamUser, UserTitlePipe } from '@core';
import { SortHighlightsOptionEnum, TreeFilterItemType, TreeFilterOption } from '@patent/types';
import { PatentViewHighlightService } from '@core/services/patent-view-highlight/patent-view-highlight.service';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-patent-highlights-section',
  templateUrl: './patent-highlights-section.component.html',
  styleUrls: ['./patent-highlights-section.component.scss']
})
export class PatentHighlightsSectionComponent {
  @Input() isSavingHighlight: boolean;

  @Output() changeHighlightColor = new EventEmitter<{event: MouseEvent, highlight: DocumentAnnotation}>(null);
  @Output() highlightsSorted = new EventEmitter<SortHighlightsOptionEnum>();

  sortOptions: SortHighlightsOptionEnum[] = [
    SortHighlightsOptionEnum.RECENTLY_ADDED,
    SortHighlightsOptionEnum.OLDEST_ADDED,
    SortHighlightsOptionEnum.CREATOR,
    SortHighlightsOptionEnum.COLOR
  ];

  treeFilterItemType = TreeFilterItemType;

  private openingFilterPopovers: { filterOption: TreeFilterOption, popover: NgbPopover }[] = [];
  private maxPopoverDepth: number = 0;

  constructor(
    private patentViewService: PatentViewService,
    public patentViewHighlightService: PatentViewHighlightService,
    private userTitlePipe: UserTitlePipe
  ) {
  }

  get currentSort(): SortHighlightsOptionEnum {
    return this.patentViewHighlightService.highlightsSortBy;
  }

  get highlightList(): DocumentAnnotation[] {
    return this.patentViewHighlightService.highlightList;
  }

  getNameOfHighlightAuthor(highlight: DocumentAnnotation): string {
    return highlight.user?.first_name + ' ' + highlight.user?.last_name;
  }

  onChangeHighlightColor(event: MouseEvent, highlight: DocumentAnnotation) {
    this.changeHighlightColor.emit({event: event, highlight: highlight});
  }

  goToHighlight(highLight) {
    const highlightEle = document.querySelector(`[data-id="hl-${highLight.id}"]`) as HTMLElement;
    if (highlightEle) {
      highlightEle.scrollIntoView({behavior: 'smooth'});
      document.querySelectorAll('#patent-result .highlight').forEach((ele: HTMLElement) => {
        ele.style.boxShadow = 'none';
      });

      document.querySelectorAll(`[data-id="hl-${highLight.id}"]`).forEach((ele: HTMLElement) => {
        ele.style.boxShadow = '0 0 2px #135a9a4d, 0 2px 4px #135a9a4d';
        setTimeout(() => {
          ele.style.boxShadow = 'none';
        }, 5000);
      });
    }
  }

  onSortHighlightsClicked(sortOption: SortHighlightsOptionEnum) {
    this.patentViewHighlightService.highlightsSortBy = sortOption;
    this.highlightsSorted.emit(sortOption);
  }

  getSelectedHighlightsFilterOptionsTooltip() {
    const selectedOptions = this.patentViewHighlightService.findSelectedHighlightsFilterOptions();
    if (selectedOptions.length < 1 || this.patentViewHighlightService.parentOfSelectedFilterOption.isToggle) {
      return null;
    }

    const tooltip = [];

    for (let op of selectedOptions.filter(o => o.itemType === TreeFilterItemType.TEAM_USER)) {
      tooltip.push(this.userTitlePipe.transform(op.item as TeamUser));
    }

    const tooltipText = tooltip.length <= 2 ? tooltip.join(' and ') : tooltip.slice(0, -1).join(', ') + ' and ' + tooltip[tooltip.length - 1];
    return `Filter by ${tooltipText}`;
  }

  filterHighlights(option: TreeFilterOption, filterPopover: NgbPopover, childFilterPopover: NgbPopover) {
    this.closeOtherOpeningFilterPopovers(option);

    if (option.hasChildren) {
      childFilterPopover.toggle();
      return;
    }

    this.patentViewHighlightService.selectHighlightsFilterOption(option);

    filterPopover.close();
    this.clearOpeningFilterPopovers();

    this.filterAndSortHighlights();
  }

  onMouseEnterFilterOption(option: TreeFilterOption, childFilterPopover: NgbPopover) {
    if (childFilterPopover) {
      this.closeOtherOpeningFilterPopovers(option);
      this.trackingOpenedFilterPopovers(option, childFilterPopover);
      childFilterPopover.open();
    }
  }

  onHighlightsFilterPopoverClosed() {
    this.clearOpeningFilterPopovers();
  }

  private trackingOpenedFilterPopovers(option: TreeFilterOption, filterPopover: NgbPopover) {
    if (option.hasChildren) {
      const isExisting = this.openingFilterPopovers.find((p) => this.patentViewHighlightService.isTreeFilterOptionEqual(p.filterOption, option));
      if (!isExisting) {
        this.openingFilterPopovers.push({
          filterOption: option,
          popover: filterPopover
        });
      }
      this.maxPopoverDepth = option.depth;
    }
  }

  private clearOpeningFilterPopovers() {
    this.openingFilterPopovers = [];
    this.maxPopoverDepth = 0;
  }

  private closeOtherOpeningFilterPopovers(option: TreeFilterOption) {
    if (this.maxPopoverDepth >= option.depth) {
      this.openingFilterPopovers.forEach((p) => {
        if (p.filterOption.depth >= option.depth && !this.patentViewHighlightService.isTreeFilterOptionEqual(p.filterOption, option)) {
          p.popover.close();
        }
      });
      this.openingFilterPopovers = this.openingFilterPopovers.filter((p) => p.filterOption.depth < option.depth);
    }
  }

  private filterAndSortHighlights() {
    this.patentViewHighlightService.filterHighlights();
    this.patentViewHighlightService.sortHighlights();
  }
}
