import { Component, EventEmitter, Input, OnDestroy, AfterViewInit, Output, ViewChild, OnInit } from '@angular/core';
import { Patent, TeamUser, UserGroup } from '@core/models';
import { AnnotationService, DocumentAnnotation, DocumentCommentSourceEnum, PatentService, PatentViewService, ToastService, ToastTypeEnum, UserService } from '@core/services';
import { AnnotationStoreService } from '@core/store';
import { Subscription } from 'rxjs';
import { PatentCommentEditorComponent } from '../patent-comment-editor/patent-comment-editor.component';
import { FlagSizeEnum } from '@core/services/patent/utils/countryCode';

@Component({
  selector: 'app-patent-comment-box',
  templateUrl: './patent-comment-box.component.html',
  styleUrls: ['./patent-comment-box.component.scss']
})
export class PatentCommentBoxComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
  @Input() teamUsers: TeamUser[];
  @Input() teamGroups: UserGroup[];
  @Output() deleteComment = new EventEmitter(true);
  @Output() replyComment = new EventEmitter(true);
  @Input() otherFamilyComment?: boolean;
  @Input() showDivider = false;

  showReplies: boolean;
  addReply: boolean;
  editComment: DocumentAnnotation;
  commentSource = DocumentCommentSourceEnum;
  private subscriptions = new Subscription();
  private _comment: DocumentAnnotation;

  @ViewChild('replyEditor') private replyEditor: PatentCommentEditorComponent;

  get comment(): DocumentAnnotation {
    return this._comment;
  }

  @Input() set comment(value: DocumentAnnotation) {
    this._comment = value;
    this.showReplies = value?.show_replies;
  }

  constructor(
    public userService: UserService,
    private toastService: ToastService,
    private patentViewer: PatentViewService,
    private patentService: PatentService,
    public annotationService: AnnotationService,
    public annotationStore: AnnotationStoreService,) { }

  ngOnInit(): void {
    this.showReplies = this.comment?.show_replies;
  }

  get patent(){
    return this.patentViewer.patent;
  }

  ngAfterViewInit(){
    this.comment.tagged_user_ids = this.annotationService.extractTaggedUserIds(this.comment.comment);
    this.comment.tagged_group_ids = this.annotationService.extractTaggedGroupIds(this.comment.comment);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  getFlagIcon(publicationNumber: string) {
    return this.patentService.getFlagCssByPublication(publicationNumber, FlagSizeEnum.MD).toLowerCase();
  }
  loadPublication(c: DocumentAnnotation){
    const queryParam = `?comment_id=${c.id}`;
    window.open(`${this.patentViewer.getPatentViewerBaseUrl(c.document_id, c.publication_number)}${queryParam}`, '_blank');
  }

  toggleReplies(event) {
    this.showReplies = !this.showReplies;
  }

  onAddReply(event){
    this.addReply = true;
    setTimeout(() => { this.replyEditor.focusEditor(); }, 50);
  }

  isCommentOwner(userId: number): boolean {
    return userId && this.userService.getUser()?.profile?.id === userId;
  }

  onEditComment(cm) {
    this.editComment = cm;
  }

  onCancelComment(cm?: DocumentAnnotation) {
    this.editComment = null;
    if(cm && cm.replies.length === 0){
      this.showReplies = false;
    }
    if(this.addReply){
      this.addReply = false;
    }
    document.body.click();
  }

  onDeleteComment(cm) {
    this.deleteComment.emit(cm);
  }

  onUpdateComment(payload, comment) {
    const updateDocumentComment$ = this.annotationService.updateDocumentComment(payload, comment.document_id, comment.id)
      .subscribe({
        next: ({ data }) => {
          const comments = this.annotationStore.listComments;
          const index = comments.findIndex(r => r.id === comment.id);
          if (index > -1) {
            data.user = this.userService.getUser().profile;
            comments[index] = data;
            this.annotationStore.listComments = [...comments];
            this.annotationService.highlightComments(true, data);
            this.onCancelComment();
            this.toastService.show({
              type: ToastTypeEnum.SUCCESS,
              header: 'Comment updated',
              body: `The comment has been updated successfully.`,
              delay: 5000
            });
          }
        }, error: err => { console.error(err);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Failed to Update a comment',
            body: `There was an error in updating the comment.<br/>${err.error.message}`,
            delay: 5000
          });
        }
      });
    this.subscriptions.add(updateDocumentComment$);
  }

  onSaveReply(event) {
    const payload: DocumentAnnotation = {
      end_pos: this.comment.end_pos,
      field: this.comment.field,
      start_pos: this.comment.start_pos,
      text: this.comment.text,
      publication_number: this.comment.publication_number,
      source: this.comment.source,
      color: '',
      comment: event.comment,
      parent_comment_id: event.parent_comment_id,
      tagged_user_ids: event.tagged_user_ids,
      tagged_group_ids: event.tagged_group_ids
    };
    const saveSectionComment$ = this.annotationService.saveDocumentComment(payload, this.comment.document_id)
      .subscribe({next:({data}) => {
        data.user = this.userService.getUser().profile;
        this.annotationStore.addComment(data);
        this.annotationService.highlightComments(true, data);
        this.onCancelComment();
        this.toastService.show({
          type: ToastTypeEnum.SUCCESS,
          header: 'Comment reply submitted',
          body: `The reply for the comment is submitted successfully`,
          delay: 5000
        });
      }, error: err => { console.error(err); this.toastService.show({
        type: ToastTypeEnum.ERROR,
        header: 'Failed to reply a comment',
        body: `There was an error in replying the comment.<br/>${err.error.message}`,
        delay: 5000
      });}});
    this.subscriptions.add(saveSectionComment$);
  }

  scrollToCommentHighlight(comment){
    if(this.otherFamilyComment){
      return;
    }
    this.annotationService.scrollToHighlightedElement(comment);
  }

  onMouseOverComment(comment){
    if(this.otherFamilyComment){ return }
    this.annotationService.focusinComment(comment);
  }

  onMouseLeaveComment(comment){
    if(this.otherFamilyComment){ return }
    this.annotationService.focusoutComment(comment);
  }

  disablePrivate(comment: DocumentAnnotation): boolean{
    return comment.replies?.length>0 || comment.tagged_user_ids?.length>0 || comment.tagged_group_ids?.length>0;
  }

  onMakeCommentPublic(comment: DocumentAnnotation){
    this.onUpdateComment({private: false}, comment);
  }

  onMakeCommentPrivate(comment: DocumentAnnotation){
    this.onUpdateComment({private: true}, comment);
  }

  getDisplayedReplies(comment: DocumentAnnotation){
    return comment.replies || [];
  }

}
