import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';

import { PatentCommentBoxComponent } from './patent-comment-box.component';
import { DocumentAnnotation } from '@core/services/annotation/types';
import { provideMatomo } from 'ngx-matomo-client';
import { UserTitlePipe } from '@core';

describe('PatentCommentBoxComponent', () => {
  let component: PatentCommentBoxComponent;
  let fixture: ComponentFixture<PatentCommentBoxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentCommentBoxComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        UserTitlePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PatentCommentBoxComponent);
    component = fixture.componentInstance;
    component.comment = {
      end_pos: 0,
      field: 'abstract',
      start_pos: 0,
      text: 'comment reference text',
      color: 'ffffff',
      comment: 'comment text',
      parent_comment_id: null,
      tagged_user_ids: [],
      tagged_group_ids: []
    } as DocumentAnnotation;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
