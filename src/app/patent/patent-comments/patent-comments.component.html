<div class="p-spacing-md">
  <app-patent-side-section-header [title]="sideSectionEnum.COMMENTS" (closeEvent)="onCloseSectionClicked()" [tooltip]="tooltip" [tooltipClass]="tooltipClass"
    class="m-b-spacing-big d-block">
  </app-patent-side-section-header>
  <div class="w-100 d-flex justify-content-between comments-menu m-b-spacing-x-big">
    <button #permissionSettingButton ngbTooltip="Default settings" [autoClose]="true" triggers="hover"
            class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small"
            (click)="permissionSettingPopper.show(permissionSettingButton)">
      <i class="fa-regular fa-lg fa-gear"></i>
    </button>
    <div class="d-flex gap-spacing-xx-s">
      <div class="figma-dropdown comment-filter-by">
        <button [ngbPopover]="popoverFilterCommentsTemplate"
                #filterPopover="ngbPopover"
                [popoverContext]="{options: annotationService.commentsFilterOptions, showHeader: true}"
                [autoClose]="'outside'" popoverClass="inline-menu-popper"
                container="body" placement="bottom-right"
                class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small"
                [class.content-color-secondary]="annotationService.isCommentsFilterByDefault()"
                [class.content-color-primary]="!annotationService.isCommentsFilterByDefault()"
                (hidden)="onCommentsFilterPopoverClosed()"
                [ngbTooltip]="getSelectedCommentsFilterOptionsTooltip()"
                tooltipClass="white-tooltip text-left">
          <i class="fa-regular fa-lg fa-bars-filter"></i>
          {{ annotationService.parentOfSelectedFilterOption.item }}
          <span *ngIf="annotationService.parentOfSelectedFilterOption.multiple && annotationService.commentsFilterBy.length > 0"
                class="badge-circle content-label-small content-color-reversed-grey figma-bg-reversed">
            {{ annotationService.commentsFilterBy.length }}
          </span>
        </button>
      </div>
      <div class="figma-dropdown comment-sort-by">
        <button [ngbPopover]="popoverSortCommentsTemplate" [autoClose]="true" popoverClass="context-menu-popper"
          container="body" placement="bottom-right"
          class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small button-square"
          [class.content-color-secondary]="annotationService.isCommentsSortByDefault()"
          [class.content-color-primary]="!annotationService.isCommentsSortByDefault()">
          <i class="fa-regular fa-lg fa-arrow-up-arrow-down"></i>
        </button>
      </div>
    </div>
  </div>

  <div class="w-100 d-flex justify-content-between comments-top m-b-spacing-x-big">
    <span class="tab-pill" (click)="setSelectedTab(selectedTabEnum.GENERAL)" [ngClass]="{'active': isGeneralTab}">
      <span class="content-label-medium">General <span *ngIf="generalComments.length !== 0" class="comment-count"
          [ngClass]="{'active': isGeneralTab}"> {{generalComments.length}}</span></span>
    </span>
    <span class="tab-pill" (click)="setSelectedTab(selectedTabEnum.SECTION)" [ngClass]="{'active': isSectionTab}">
      <span class="content-label-medium">On section <span *ngIf="sectionComments.length !== 0" class="comment-count"
          [ngClass]="{'active': isSectionTab}"> {{sectionComments.length}}</span></span>
    </span>
  </div>

  <app-patent-comment-editor class="comments-add d-flex align-items-center w-100" *ngIf="isGeneralTab"
    [allTeamUsers]="teamUsers" [allTeamGroups]="teamGroups" (cancelComment)="onCancelComment()"
    (saveComment)="onSaveGeneralComment($event)"></app-patent-comment-editor>
</div>

<div class="figma-side-section-content w-100 flex-grow-1">
  <div class="w-100 h-100 d-flex flex-column align-items-stretch comments-result" [class.loading-comments]="isLoading" [class.disabled-comments]="isDisabledClick">

    <div class="comments-main-content flex-grow-1 m-b-spacing-md p-r-spacing-xxx-s" [class.m-t-spacing-none]="isCommentsEmpty">
      <div *ngIf="!isLoading">
        <div class="comments-no-comment p-spacing-big" *ngIf="isCommentsEmpty">
          <div class="d-flex flex-column align-items-center">
            <div class="p-b-spacing-xx-big">
              <div class="feature-container feature-xl"><i class="feature-icon fa-light fa-message-lines" [ngClass]="isSectionTab? 'fa-message-pen':'fa-message-lines'"></i></div>
            </div>
            <div class="content-color-tertiary p-y-spacing-sm">
              <ng-container *ngIf="isFiltered else noFiltered">
                <span class="content-heading-h5 d-block p-b-spacing-sm">No results found</span>
                <span class="d-block p-b-spacing-sm">It seems we can’t find any result based on your selection.</span>
                <span>Try to clear or change your filters.</span>
              </ng-container>
              <ng-template #noFiltered>
                <ng-container *ngIf="isGeneralTab">
                  <span class="d-block p-b-spacing-sm">Currently, there are no general comments on this patent.</span>
                  <span>Be the first to add a comment!</span>
                </ng-container>
                <span *ngIf="isSectionTab">Highlight a piece of text and make a comment. We will display here.</span>
              </ng-template>
            </div>
          </div>
        </div>
        <div class="comments-list">
          <app-patent-comment-box class="w-100" *ngFor="let v of currentDisplayedComments; let last = last; index as i; trackBy: trackDisplayComment"
            [comment]="v" (deleteComment)="onDeleteComment($event)"
            [teamUsers]="teamUsers" [teamGroups]="teamGroups" [showDivider]="!last">
          </app-patent-comment-box>

          <div *ngIf="otherComments.length > 0" class="comments-list-other">
            <div class="m-y-spacing-big m-x-spacing-big">
              <span class="content-heading-h5">Other family members </span>
              <span style="font-size: 0.75rem; width: 1rem; height: 1rem;" ><i class="fa-light fa-info-circle" [ngbTooltip]="otherCommentsTooltipTemplate"
                tooltipClass="tooltip-md" container="body"></i></span>
            </div>
            <app-patent-comment-box class="w-100" [otherFamilyComment]="true" *ngFor="let v of otherComments; let last = last; index as i; trackBy: trackDisplayComment"
              [comment]="v" (deleteComment)="onDeleteComment($event)"
              [teamUsers]="teamUsers" [teamGroups]="teamGroups" [showDivider]="!last">
            </app-patent-comment-box>
          </div>
        </div>
      </div>
      <div class="loading-message" *ngIf="isLoading">
        Comments are loading...
      </div>
    </div>
  </div>
</div>

<ng-template #otherCommentsTooltipTemplate>
  <div class="text-left p-spacing-sm">
    <div class="content-heading-h6 m-b-spacing-md">Other family members</div>
    <div class="content-body-small">
      Here you can view comments on other documents of the same family. Click on each to jump to the related document.
    </div>
  </div>
</ng-template>

<ng-template #popoverFilterCommentsTemplate let-options="options" let-showHeader="showHeader">
  <div *ngIf="showHeader" class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-none p-x-spacing-md p-t-spacing-md p-b-spacing-xx-s">Filter by</div>
  <div class="d-flex flex-column gap-spacing-xx-s comments-filter-content"
       appScrollbarDetector scrollbarCss="has-scrollbar">
    <ng-container *ngFor="let f of options; let first=first; let last=last;">
      <div [ngbTooltip]="f.disabledMessage" tooltipClass="white-tooltip">
        <div (click)="filterComments(f, filterPopover, childFilterPopover)"
             (mouseenter)="onMouseEnterFilterOption(f, childFilterPopover)"
             [class.m-t-spacing-sm]="first" [class.m-b-spacing-sm]="last"
             [class.active]="f.selected || annotationService.hasSelectedCommentsFilterChildren(f)"
             [class.disabled]="(f.hasChildren && !f.children?.length) || f.isDisabled"
             [class.figma-dropdown-item-check]="f.isToggle"
             class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-default-bg content-body-medium m-x-spacing-sm m-b-spacing-none p-x-spacing-md p-y-spacing-sm">
          <div [ngbPopover]="f.hasChildren && f.children?.length ? popoverFilterCommentsTemplate : null"
               #childFilterPopover="ngbPopover"
               [popoverContext]="{options: f.children, showHeader: false}"
               [autoClose]="false" [placement]="['left-top']"
               [popoverClass]="'inline-menu-popper ' + (f.hasChildren ? 'm-r-spacing-md' : '')"
               triggers="manual" container="body"
               class="d-flex justify-content-between align-items-center gap-spacing-sm w-100">
            @if (f.isCheckbox) {
              <label [class.active]="f.selected" class="figma-checkbox align-items-center">
                <input type="checkbox" [checked]="f.selected"/>
                @switch (f.itemType) {
                  @case (treeFilterItemType.TEAM_USER) {
                    <app-user-avatar [user]="f.item" [hasSubTitle]="true" size="xsmall" subtitleMaxWidth="10.625rem"
                                     class="d-block" [showYouSuffix]="true"
                                     [showTooltip]="true" [showUserTitleOnTooltip]="false" [showUserEmailOnTooltip]="true"
                                     (click)="$event.preventDefault(); $event.stopImmediatePropagation(); filterComments(f, filterPopover, childFilterPopover)">
                    </app-user-avatar>
                  }
                  @default {
                    <span>{{ f.item }}</span>
                  }
                }
              </label>
            } @else {
              <div class="p-r-spacing-xxx-big">
                @switch (f.itemType) {
                  @case (treeFilterItemType.TEAM_USER) {
                    <app-user-avatar [user]="f.item" [hasSubTitle]="true" size="xsmall" subtitleMaxWidth="10.625rem"
                                     class="d-block"
                                     [showTooltip]="true" [showUserTitleOnTooltip]="false" [showUserEmailOnTooltip]="true">
                    </app-user-avatar>
                  }
                  @default {
                    {{ f.item }}
                  }
                }
              </div>
            }
            <i *ngIf="f.hasChildren" class="fa-regular fa-chevron-right p-spacing-s fa-fw fa-1x"></i>
          </div>
        </div>
      </div>
      <div *ngIf="f.showDivider && options.length > 1" class="figma-dropdown-item-divider"></div>
    </ng-container>
  </div>
</ng-template>

<ng-template #popoverSortCommentsTemplate>
  <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-md">Sort by</div>
  <div class="figma-dropdown-item figma-dropdown-item-hover figma-dropdown-item-check p-r-spacing-s content-body-medium"
       *ngFor="let s of annotationService.commentsSortByOption" (click)="sortComments(s);"
       [class.active]="annotationService.commentsSortBy === s">
    <span class="p-r-spacing-xxx-big">{{ s }} </span>
  </div>
</ng-template>

<app-popper #permissionSettingPopper placement="bottom" [showArrow]="false" customClass="permission-setting-popper">
  <app-permission-setting [permissionKey]="commentPermissionKey" [popper]="permissionSettingPopper">
  </app-permission-setting>
</app-popper>
