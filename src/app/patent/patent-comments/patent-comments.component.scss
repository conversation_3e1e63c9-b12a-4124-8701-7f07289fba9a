@import 'scss/figma2023/variables';
@import 'scss/figma2023/mixins';

.comments-top {
  > * {
    width: 50%;
  }
}
.comments-result {

  .comments-selected-text-container {
    margin-top: $spacing-system-spacing-x-big;
    height: 3.625rem;

    .comments-vertical-bar {
      background: $colour-orange-brand-500;
      width: 0.125rem;
      border-radius: $spacing-system-spacing-x-s;
      margin-right: $spacing-system-spacing-sm;
    }

    .comments-selected-text {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
  .comments-add{
    margin-top: $spacing-system-spacing-x-big;
  }

  .comments-main-content {
    margin-top: $spacing-system-spacing-x-big;
    overflow: auto;
  }


  .comments-no-comment {
    text-align: center;
    font-size: $spacing-system-spacing-big;
  }
  .loading-message{
    display: none;
    align-items: center;
    justify-content: center;
  }

  &.loading-comments{
    .comments-add{
      min-height: 56px;
    }
    .tab-pill{
      color: transparent;
      user-select: none;
      cursor: unset;
      &:not(:last-child) { margin-right: $spacing-system-spacing-sm; }
    }
    .loading-message{
        display: flex !important;
        height: 100%;
    }
  }
  .comments-list-other{
    border-top: 2px solid $colours-border-moderate;
    padding-top: $spacing-system-spacing-sm;
    margin-top: $spacing-system-spacing-sm;
  }
  .comment-count{
    border-radius: 25rem;
    padding: 0.157rem $spacing-system-spacing-x-s;
    &.active{
      background-color: $colour-blue-brand-200;
    }
  }
}

:host::ng-deep {
  .disabled-comments {
    pointer-events: none !important;
    cursor: wait;
    opacity: .5;
  }

  .loading-comments {
    .tab-pill,
    .comments-add,
    .comments-main-content,
    .comments-list {
      * {
        display: none !important;
      }

      @include loading-box-style();
    }
  }
}

:host {
  .badge-circle {
    height: 1.25rem;
    width: 1.25rem;
    line-height: 1.125rem;
    padding: 0 !important;
  }
}

::ng-deep {
  .figma-dropdown-default-bg {
    .figma-checkbox.active {
      background-color: $colours-background-bg-secondary !important;
    }
  }

  .comments-filter-content {
    max-height: 14.4rem;
    overflow-y: auto;
    &.has-scrollbar {
      margin-right: 3px;
    }
  }
}
