import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';

import { PatentCommentEditorComponent } from './patent-comment-editor.component';
import { provideMatomo } from 'ngx-matomo-client';
import { UserTitlePipe } from '@core';

describe('PatentCommmentEditorComponent', () => {
  let component: PatentCommentEditorComponent;
  let fixture: ComponentFixture<PatentCommentEditorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PatentCommentEditorComponent ],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        UserTitlePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PatentCommentEditorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
