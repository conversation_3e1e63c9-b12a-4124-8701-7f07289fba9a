import { AfterViewInit, Component, ElementRef, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { Collaborator, TeamUser, UserGroup } from '@core/models';
import { AnnotationService, DocumentAnnotation, ToastService, ToastTypeEnum, UserService } from '@core/services';
import { TextUtil } from '@core/utils';
import { MentionConfig } from 'angular-mentions';
import { Subscription } from 'rxjs';
import { PopperComponent } from '@shared/components/popper/popper.component';

@Component({
  selector: 'app-patent-comment-editor',
  templateUrl: './patent-comment-editor.component.html',
  styleUrls: ['./patent-comment-editor.component.scss']
})
export class PatentCommentEditorComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() edit?: DocumentAnnotation;
  @Input() reply?: DocumentAnnotation;
  @Input() allTeamUsers: TeamUser[] = [];
  @Input() allTeamGroups: UserGroup[] = [];
  @Input() focused?: boolean;
  @Input() maxChar?: number = 500;
  @Input() placeholder?: string = "Add a comment to this patent";
  @Input() hasOuterBorder?: boolean;

  @Output() cancelComment = new EventEmitter(true);
  @Output() saveComment = new EventEmitter(true);
  @Output() updateComment = new EventEmitter(true);
  @Output() mentionSelected = new EventEmitter<void>();
  @Output() mentionClosed = new EventEmitter<void>();
  @ViewChild('commentDialog') commentDialog: ElementRef;
  @ViewChild('commentEditor') commentEditor: ElementRef;
  @ViewChild('permissionSettingPopper') permissionSettingPopper: PopperComponent;

  commentPrivate: boolean;
  isCommentPrivateManualChanged: boolean = false;

  isSavingComment: boolean;
  mentionListOpen: boolean;
  commentText: string= '';
  inputChar: number =0;
  mentionConfig: MentionConfig = {
    triggerChar: '@',
    maxItems: 100,
    labelKey: 'name',
    allowSpace: true,
    mentionSelect: (collaborator): string => {
      this.isMentionedUserSelected = true;
      this.mentionSelected.emit();

      return ``;
    },
    mentionFilter: (searchString: string, items?) => {
      if (!searchString || !searchString.trim()) {
        return items;
      }
      const val = searchString.toLowerCase().trim();
      return items?.filter((i) => i.name.toLowerCase().includes(val));
    }
  } as MentionConfig;
  visibleCollaborators: Collaborator[] = [];

  readonly commentPermissionKey = UserService.DEFAULT_COMMENT_PERMISSION_KEY;

  private taggedUsers: TeamUser[] = [];
  private taggedGroups: UserGroup[] = [];
  private subscriptions = new Subscription();
  private isMentionedUserSelected: boolean = false;

  constructor(
    public annotationService: AnnotationService,
    private toastService: ToastService,
    public userService: UserService,
  ) {
  }

  private get commentInputValue(): string {
    return this.commentEditor?.nativeElement?.innerHTML?.trim();
  }

  get disablePrivate(): boolean{
    return this.edit?.replies?.length >0 || this.taggedUsers.length>0 || this.taggedGroups.length>0;
  }

  get defaultCommentPermission(){
    return this.userService.defaultCommentPermission;
  }

  get isEditorEmpty(): boolean{
    return !this.getCommentText();
  }
  ngAfterViewInit(): void {
    if(this.focused || this.edit){
      this.focusEditor();
    }
  }
  focusEditor(){
    setTimeout(() => { this.isSavingComment = false; this.focused = true; this.commentEditor.nativeElement.focus(); }, 0);
  }
  ngOnInit() {
    if(this.edit){
      this.commentText = this.edit.comment;
      this.commentPrivate = this.edit.private;
    } else {
      this.commentPrivate = this.defaultCommentPermission;
    }

    this.subscriptions.add(this.userService.permissionSettingChanged$.subscribe({
      next: (settings) => {
        if (!this.isCommentPrivateManualChanged && !this.edit) {
          this.commentPrivate = this.defaultCommentPermission;
        }
      }
    }));
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onChangePermission(newValue) {
    if(this.disablePrivate) return;
    this.commentPrivate = newValue;
    this.isCommentPrivateManualChanged = true;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event) {
    if (this.isMentionedUserSelected) {
      this.isMentionedUserSelected = false;
      return;
    }

    if (!this.mentionListOpen && this.focused) {
      if (!this.commentDialog.nativeElement.contains(event.target)) {
        this.focused = false;
      }
    }
  }

  private getCommentText() {
    const commentHtml = this.commentInputValue;
    if (commentHtml) {
      return commentHtml.replace(/<input.*?data-(user|group)-id="\d+".*?value=".+?".*?>/gi, (val) => {
        const infoRegex = new RegExp(/data-(user|group)-id="(\d+)".*?value="@(.*?)"/, 'gi');
        const match = infoRegex.exec(val);
        return `[${match[1] != 'user' ? match[1] : ''}@${match[2]}=${match[3]}]`;
      });
    }

    return null;
  }
  private tagCollaborator(collaborator: Collaborator) {
    const skip = !collaborator ||
      collaborator.type == 'user' && this.taggedUsers.find((u) => u.id === collaborator.id) ||
      collaborator.type == 'group' && this.taggedGroups.find((u) => u.id === collaborator.id);

    if (skip) {
      return;
    }

    const fontName = 'Open Sans Regular, sans-serif';
    const fontSize = '16.6px/25px';
    const fontWeight = 400;
    const textWidth = TextUtil.calculateTextWidth(collaborator.name, fontName, fontSize, fontWeight);

    const node = document.createElement('input');
    node.type = 'text';
    node.className = `lcd-tagged-${collaborator.type}`;
    node.readOnly = true;
    node.setAttribute(`data-${collaborator.type}-id`, collaborator.id.toString());
    node.setAttribute('value', '@'+collaborator.name);
    node.style.cssText = `width: ${textWidth+10}px;`;

    try {
      this.insertInputAndSetCursor(node);
    } catch (e) {
      console.error(e);
    }
  }

  private insertInputAndSetCursor(inputNode: HTMLInputElement): void {
    try {
      const containerDiv = this.commentEditor.nativeElement;

      const selection = window.getSelection();
      const range = selection.getRangeAt(0);

      range.insertNode(inputNode);

      const spaceNode = document.createTextNode('\u00A0');

      const normalTextSpan = document.createElement('span');
      normalTextSpan.className = 'normal-text';
      normalTextSpan.appendChild(spaceNode);

      const newRange = document.createRange();
      newRange.setStartAfter(inputNode);
      newRange.collapse(true);
      newRange.insertNode(normalTextSpan);

      newRange.setStartAfter(spaceNode);
      newRange.collapse(true);

      selection.removeAllRanges();
      selection.addRange(newRange);

      containerDiv.focus();
    } catch (e) {
      console.error('Error in insertInputAndSetCursor:', e);
    }
  }

  updateTaggedCollaborators(collaborator = null) {
    if (collaborator) {
      const editor = this.commentEditor.nativeElement;
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);

      const textBeforeCursor = this.getTextBeforeCursor(editor, range);
      const lastAtPos = textBeforeCursor.lastIndexOf('@');

      if (lastAtPos !== -1) {
        const startRange = document.createRange();
        const container = this.findContainerAndOffset(editor, lastAtPos);
        startRange.setStart(container.node, container.offset);
        startRange.setEnd(range.endContainer, range.endOffset);
        startRange.deleteContents();

        this.tagCollaborator(collaborator);

      } else {
        this.tagCollaborator(collaborator);
      }
    }

    if (collaborator && this.mentionListOpen) {
      setTimeout(() => { this.mentionListOpen = false; }, 100);
    }

    this.setTaggedCollaborators(this.getCommentText());
  }

  private getTextBeforeCursor(editor: HTMLElement, range: Range): string {
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(editor);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString();
  }

  private findContainerAndOffset(container: Node, offset: number): { node: Node; offset: number } {
    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      null
    );

    let currentNode = walker.nextNode();
    let currentOffset = 0;

    while (currentNode) {
      const nodeLength = currentNode.textContent.length;
      if (currentOffset + nodeLength > offset) {
        return {
          node: currentNode,
          offset: offset - currentOffset
        };
      }
      currentOffset += nodeLength;
      currentNode = walker.nextNode();
    }

    return {
      node: container,
      offset: offset
    };
  }

  private setTaggedCollaborators(commentText: string) {
    const taggedUserIds = this.annotationService.extractTaggedUserIds(commentText);
    const taggedGroupIds = this.annotationService.extractTaggedGroupIds(commentText);
    this.taggedUsers = this.allTeamUsers.filter((o) => taggedUserIds.includes(o.id));
    this.taggedGroups = this.allTeamGroups.filter((o) => taggedGroupIds.includes(o.id));
    const visibleTeamUsers = this.allTeamUsers.filter((o) => !taggedUserIds.includes(o.id)).map((o) => {
      return {
        id: o.id,
        type: "user",
        name: o.display_name,
        email: o.email
      } as Collaborator
    });
    const visibleTeamGroups = this.allTeamGroups.filter((o) => !taggedUserIds.includes(o.id)).map((o) => {
      return {
        id: o.id,
        type: "group",
        name: o.name
      } as Collaborator
    });
    this.visibleCollaborators = [...visibleTeamUsers, ...visibleTeamGroups];
    if(this.disablePrivate && this.commentPrivate){
      this.commentPrivate = false;
      this.toastService.show({
        type: ToastTypeEnum.INFO,
        header: 'Comment switch to public',
        body: `By mentioning someone else, the comment is posted automatically as public.`,
        delay: 5000
      });
    }
  }

  onSaveComment(){
    if(this.edit){
      this.updateComment.emit(this.getPayload());
    } else {
      this.saveComment.emit(this.getPayload());
    }
    this.resetEditor();
  }

  onCancelComment(){
    this.cancelComment.emit(true);
    this.resetEditor();
  }

  onInput(event){
    this.inputChar = event.target.textContent.length;

    const editor = this.commentEditor.nativeElement;
    if (this.mentionListOpen) {
      this.highlightMentionText();
    }

    this.updateTaggedCollaborators();
  }

  private highlightMentionText() {
    const editor = this.commentEditor.nativeElement;
    const selection = window.getSelection();
    const range = selection.getRangeAt(0);

    const currentTextNode = range.startContainer;
    if (currentTextNode.nodeType !== Node.TEXT_NODE) return;

    const text = currentTextNode.textContent || '';
    const cursorOffset = range.startOffset;

    const lastAtPos = text.lastIndexOf('@', cursorOffset);
    if (lastAtPos === -1) return;

    const beforeText = text.substring(0, lastAtPos);
    const mentionText = text.substring(lastAtPos, cursorOffset);
    const afterText = text.substring(cursorOffset);

    const mentionSpan = document.createElement('span');
    mentionSpan.className = 'text-mentioned';
    mentionSpan.textContent = mentionText;

    const textBefore = document.createTextNode(beforeText);
    const textAfter = document.createTextNode(afterText);

    const afterSpan = document.createElement('span');
    afterSpan.className = 'normal-text';
    afterSpan.appendChild(textAfter);

    const parentNode = currentTextNode.parentNode;
    parentNode.insertBefore(textBefore, currentTextNode);
    parentNode.insertBefore(mentionSpan, currentTextNode);
    parentNode.insertBefore(afterSpan, currentTextNode);
    parentNode.removeChild(currentTextNode);

    const newRange = document.createRange();
    newRange.setStart(mentionSpan.firstChild, mentionText.length);
    newRange.setEnd(mentionSpan.firstChild, mentionText.length);
    selection.removeAllRanges();
    selection.addRange(newRange);
  }

  resetEditor(){
    this.commentEditor.nativeElement.innerHTML = '';
    this.inputChar = 0;
  }

  getPayload(){
    this.isSavingComment = true;
    const commentText = this.getCommentText();
    if (commentText === '') {
      return;
    }
    let payload = {
      comment: commentText,
      color: '',
      id: this.edit ? this.edit.id : null,
      private: this.commentPrivate,
      tagged_user_ids: this.annotationService.extractTaggedUserIds(commentText),
      tagged_group_ids: this.annotationService.extractTaggedGroupIds(commentText)
    };
    if (this.reply) {
      payload['parent_comment_id'] = this.reply.id;
      if (this.edit) {
        payload.id = this.edit.id;
      } else {
        delete payload.id
      }
    }
    return payload;
  }
  onPaste(event: ClipboardEvent){
    event.preventDefault();
    event.stopPropagation();

    const clipboardText = event.clipboardData.getData('text/plain');
    const selection = window.getSelection();

    if (!selection.rangeCount) return;

    const range = selection.getRangeAt(0);
    range.deleteContents();

    const textNode = document.createTextNode(clipboardText);
    range.insertNode(textNode);

    const newRange = document.createRange();
    newRange.setStartAfter(textNode);
    newRange.setEndAfter(textNode);

    selection.removeAllRanges();
    selection.addRange(newRange);

  }

  getSubmitText(){
    if(this.edit ){
      return 'Update';
    }
    if(this.reply ){
      return 'Reply';
    }
    return 'Save';
  }

  onMentionListClosed() {
    this.mentionClosed.emit();
  }

  onPermissionSettingChanged($event: any) {
    const ele = document.getElementById('input-dialog-textarea');
    if (ele) {
      ele.click();
    }
  }
}
