<div class="input-dialog scrollbar-2024" [class.outer-border]="hasOuterBorder" [class.input-dialog-focused]="focused" #commentDialog >
  <div class="input-dialog-body">
    <div class="show-on-focus justify-content-between position-relative">
      <div class="d-flex align-items-center flex-grow-1">
        <app-user-avatar [user]="edit ? edit.user: userService.getUser().profile" [hasSubTitle]="false" size="small" class="input-user-avatar" [showTooltip]="false"></app-user-avatar>
        <div class="flex-grow-1 ms-2 input-user-name">
          <div class="content-heading-h5">{{ (edit ? edit.user: userService.getUser().profile) | userTitle }}</div>
        </div>
      </div>
      <div *ngIf="!reply">
        <div class="figma-dropdown">
          <div #permissionDropdownToggle appDropdownToggle [closeOnClick]="true"
               class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small content-color-tertiary"
               (click)="permissionSettingPopper.hide()">
            <span>
              <i class="fa-regular fa-lock  m-r-spacing-xx-s" *ngIf="commentPrivate"></i>
              <i class="fa-regular fa-globe  m-r-spacing-xx-s" *ngIf="!commentPrivate"></i>
              <i class="fas fa-caret-down"></i>
            </span>
          </div>
          <div class="figma-dropdown-content p-spacing-none figma-dropdown-content-right radius-big">
            <div class="inline-modal-container">
              <div class="inline-modal-block">
                <div class="content-heading-h6 content-color-secondary p-b-spacing-sm">Who has access</div>
                <div class="w-100">
                  <div
                    class="figma-dropdown-item figma-dropdown-item-hover p-x-spacing-sm p-y-spacing-x-s d-flex align-items-center justify-content-between gap-spacing-sm"
                    [class.active]="!commentPrivate" (click)="onChangePermission(false)">
                    <div class="d-flex flex-column align-items-start justify-content-start gap-spacing-xxx-s">
                      <div class="content-heading-h6 d-flex align-items-center gap-spacing-xx-s">
                        <i class="fa-regular fa-globe"></i>
                        <div>Public</div>
                      </div>
                      <div class="content-body-xsmall content-color-tertiary">Everyone in your company</div>
                    </div>
                    <i class="fa-regular fa-check" [class.invisible]="commentPrivate"></i>
                  </div>
                  <div
                    class="figma-dropdown-item figma-dropdown-item-hover p-x-spacing-sm p-y-spacing-x-s d-flex align-items-center justify-content-between gap-spacing-sm"
                    [class.active]="commentPrivate" [class.disabled]="disablePrivate" (click)="onChangePermission(true)">
                    <div class="d-flex flex-column align-items-start justify-content-start gap-spacing-xxx-s">
                      <div class="content-heading-h6 d-flex align-items-center gap-spacing-xx-s">
                        <i class="fa-regular fa-lock"></i>
                        <div>Private</div>
                      </div>
                      <div class="content-body-xsmall content-color-tertiary">Only you</div>
                    </div>
                    <i class="fa-regular fa-check" [class.invisible]="!commentPrivate"></i>
                  </div>
                </div>
              </div>

              <ng-container *ngIf="!edit">
                <div class="popover-divider"></div>

                <div class="inline-modal-block p-x-spacing-sm p-b-spacing-sm p-t-spacing-xx-s">
                  <div
                    class="figma-dropdown-item figma-dropdown-item-hover p-x-spacing-sm p-y-spacing-x-s d-flex align-items-center justify-content-between w-100"
                    (click)="permissionSettingPopper.show(permissionDropdownToggle)"
                    [class.disabled]="defaultCommentPermission && disablePrivate">
                    <div class="d-flex align-items-center gap-spacing-xx-s">
                      <i class="fa-regular fa-gear"></i>
                      <div>Default settings</div>
                    </div>
                  </div>
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="input-dialog-textarea" class="input-dialog-textarea comments-user-mentioned d-flex align-items-center gap-spacing-sm flex-fill" (click)="focusEditor()">
      <app-user-avatar [user]="edit ? edit.user: userService.getUser().profile" [hasSubTitle]="false" size="xsmall" class="input-user-avatar p-0"
                       [showTooltip]="true" *ngIf="!focused">
      </app-user-avatar>
      <div [contentEditable]="!isSavingComment" #commentEditor (paste)="onPaste($event)" class="text-editor"
        [innerHTML]="commentText | safeHtml: null: null: {allowedTags: []} | tagParser: '16.6px/25px' : true : '@' | bypassSecurity: 'html'"
        [attr.data-placeholder]="placeholder" (focus)="focused=true" [mention]="visibleCollaborators" [mentionListTemplate]="mentionListTemplate"
        [mentionConfig]="mentionConfig" (opened)="mentionListOpen = true" (blur)="updateTaggedCollaborators()"
        (input)="onInput($event)" (itemSelected)="updateTaggedCollaborators($event)" (mentionClosed)="onMentionListClosed()"></div>
      <small class="hide-on-focus show-remaining-count" hidden>{{inputChar}}/{{maxChar}}</small>
    </div>
  </div>
  <div class="input-contorl show-on-focus">
    <button class="button-main-secondary-grey button-small m-r-spacing-md" (click)="onCancelComment()"
      [class.disabled]="isSavingComment">Cancel</button>
    <button class="button-main-primary button-small" (click)="onSaveComment()"
      [class.disabled]="isSavingComment || isEditorEmpty">{{ getSubmitText() }}</button>
  </div>
</div>

<ng-template #mentionListTemplate let-item="item">
  <app-user-avatar [user]="item" [hasSubTitle]="false" [showTooltip]="false" class="comment-avatar scrollbar-2024"></app-user-avatar>
  <span>{{item.name}}</span>
</ng-template>

<app-popper *ngIf="!reply && !edit" #permissionSettingPopper placement="bottom-end" [showArrow]="false"
            customClass="permission-setting-popper">
  <app-permission-setting *ngIf="permissionSettingPopper.isOpen" [permissionKey]="commentPermissionKey"
                          [popper]="permissionSettingPopper" (permissionSettingChanged)="onPermissionSettingChanged($event)">
  </app-permission-setting>
</app-popper>
