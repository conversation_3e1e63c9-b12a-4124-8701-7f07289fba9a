import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { SharedModule } from '@shared/shared.module';

import { PatentCommentsComponent } from './patent-comments.component';
import { provideMatomo } from 'ngx-matomo-client';
import { UserTitlePipe } from '@core';

describe('PatentCommentsComponent', () => {
  let component: PatentCommentsComponent;
  let fixture: ComponentFixture<PatentCommentsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PatentCommentsComponent],
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        UserTitlePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true })
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(PatentCommentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
