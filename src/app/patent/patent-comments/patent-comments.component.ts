import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, TrackByFunction } from '@angular/core';
import { Patent, TeamUser, UserGroup } from '@core/models';
import {
  AnnotationService,
  DocumentAnnotation,
  DocumentCommentSourceEnum,
  PatentViewService,
  ToastService,
  ToastTypeEnum,
  UserService
} from '@core/services';
import { AnnotationStoreService, BaseStoreService } from '@core/store';
import { NgbModal, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import {
  PatentCommentsActionSourceEnum,
  PatentCommentsFilterEnum,
  PatentCommentsSelectedTabEnum,
  SideSectionEnum,
  TreeFilterItemType,
  TreeFilterOption
} from '@patent/types';
import { ModalDialogComponent } from '@shared/components/modal-dialog/modal-dialog.component';
import { finalize, Subscription } from 'rxjs';
import { UserTitlePipe } from '@core';

@Component({
  selector: 'app-patent-comments',
  templateUrl: './patent-comments.component.html',
  styleUrls: ['./patent-comments.component.scss']
})
export class PatentCommentsComponent implements OnInit, OnDestroy {
  @Input() patent: Patent;
  @Input() storeService: BaseStoreService;
  @Input() teamUsers: TeamUser[];
  @Input() teamGroups: UserGroup[];
  @Input() focusedComment?: DocumentAnnotation;
  @Input() isLoading: boolean = true;

  @Input() tooltip: { title: string, description: string } = {title: null, description: null};
  @Input() tooltipClass: string = '';
  isDisabledClick: boolean;
  @Output() closeEvent: EventEmitter<void> = new EventEmitter<void>();

  selectedTabEnum = PatentCommentsSelectedTabEnum;
  treeFilterItemType = TreeFilterItemType;

  private selectedTab: PatentCommentsSelectedTabEnum = PatentCommentsSelectedTabEnum.GENERAL;
  private _actionSource: PatentCommentsActionSourceEnum = PatentCommentsActionSourceEnum.CONTROL_BAR;

  readonly sideSectionEnum = SideSectionEnum;
  readonly commentPermissionKey = UserService.DEFAULT_COMMENT_PERMISSION_KEY;
  private openingFilterPopovers: {filterOption: TreeFilterOption, popover: NgbPopover}[] = [];
  private maxPopoverDepth: number = 0;

  @Input() set actionSource(val: PatentCommentsActionSourceEnum) {
    this._actionSource = val;

    switch (val) {
      case PatentCommentsActionSourceEnum.CONTROL_BAR:
      case PatentCommentsActionSourceEnum.GENERAL_COMMENT:
        this.selectedTab = PatentCommentsSelectedTabEnum.GENERAL;
        break;
      case PatentCommentsActionSourceEnum.SECTION_COMMENT:
        this.selectedTab = PatentCommentsSelectedTabEnum.SECTION;
        break;
    }
  }

  trackDisplayComment: TrackByFunction<DocumentAnnotation> = (i, c) => `${c.id}-${c.show_replies}-${c.replies?.length}-${c.private}-${c.updated_at}`;

  private subscriptions = new Subscription();

  constructor(
    private ngbModal: NgbModal,
    public annotationService: AnnotationService,
    private patentViewer: PatentViewService,
    public annotationStore: AnnotationStoreService,
    private toastService: ToastService,
    public userService: UserService,
    private userTitlePipe: UserTitlePipe
  ) {
  }
  ngOnInit() {
    this.initComments();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  get publicationNumber(): string{
    return this.patentViewer.activePublicationName;
  }

  get isGeneralTab(): boolean{
    return this.selectedTab === PatentCommentsSelectedTabEnum.GENERAL;
  }

  get isSectionTab(): boolean{
    return this.selectedTab === PatentCommentsSelectedTabEnum.SECTION;
  }

  get isFiltered(): boolean {
    return this.annotationService.commentsFilterBy.length > 0 &&
      this.annotationService.commentsFilterBy[0].item !== PatentCommentsFilterEnum.ALL;
  }

  get currentDisplayedComments(){
    if (this.publicationNumber) {
      return this.commentsBySelectedTab.filter(c => c.publication_number === this.publicationNumber);
    } else {
      return this.commentsBySelectedTab.filter(c => !c.publication_number);
    }
  }

  get generalComments(): DocumentAnnotation[] {
    return this.annotationStore.generalComments;
  }

  get sectionComments(): DocumentAnnotation[] {
    return this.annotationStore.sectionComments;
  }

  get otherComments(): DocumentAnnotation[] {
    if (this.publicationNumber) {
      return this.commentsBySelectedTab.filter(c => c.publication_number !== this.publicationNumber);
    } else {
      return this.commentsBySelectedTab.filter(c => !!c.publication_number);
    }
  }

  get commentsBySelectedTab() {
    return this.isSectionTab ? this.sectionComments : this.generalComments;
  }

  get isCommentsEmpty(): boolean{
    return this.currentDisplayedComments?.length === 0;
  }

  setSelectedTab(tab: PatentCommentsSelectedTabEnum) {
    this.selectedTab = tab;
  }

  onCloseSectionClicked() {
    this.closeEvent.emit();
  }

  initComments(){
    this.loadUsers();
    if(this.focusedComment) {
        this.annotationService.scrollToComment(this.focusedComment);
        this.annotationService.scrollToHighlightedElement(this.focusedComment);
    }
  }

  loadUsers() {
    const userIds = [...new Set(this.annotationStore.listComments.filter(cm => !cm.user).map(a => a.user_id))];

    if (userIds.length > 0) {
      const teamUsersPayload = { id: 'in:' + userIds.join(','), 'load_all': 1, include_me: 1, include_blocked: 1 };

      const getTeamUsers$ = this.userService.getTeamUsers(teamUsersPayload)
        .subscribe({
          next: ({ users }) => {
            const mappedUser = new Map();
            for (let u of users) {
              mappedUser.set(u.id, u);
            }
            this.annotationStore.listComments.forEach(a => {
              if (mappedUser.has(a.user_id)) {
                a.user = mappedUser.get(a.user_id)
              }
            });
            this.isLoading = false;
            this.annotationService.updateCreatedByFilterOption();
          }, error: error => console.log('error in fetching user')
        });
      this.subscriptions.add(getTeamUsers$);
    }
  }

  onDeleteComment(comment){
    const modalRef = this.ngbModal.open(ModalDialogComponent, {size: 'md', backdrop: 'static', 'centered': true});
    modalRef.componentInstance.options = {
      title: 'Delete comment',
      description: 'Deleting this comment will also remove the linked answers.',
      question: 'Would you like to delete this comment?',
      confirmButton: 'Delete',
      cancelButton: 'Cancel'
    };
    modalRef.result.then((result) => {
      if (result) {
        this.isDisabledClick = true;
          const deleteDocumentComment$ = this.annotationService.deleteDocumentComment(Number(this.patent.general.docdb_family_id), comment.id)
            .subscribe({next: () => {
              this.annotationStore.listComments = this.annotationStore.listComments.filter(cm => cm.id !== comment.id);
              this.annotationService.removeCommentHighlight(comment.id);
              this.annotationService.highlightComments();
              this.toastService.show({
                type: ToastTypeEnum.SUCCESS,
                header: 'Comment deleted',
                body: `The comment was deleted successfully`,
                delay: 3000
              });
              this.isDisabledClick = false;
              this.annotationStore.removeAnnotation = 'comments';
            }, error: err => {
              console.log(err);
              this.isDisabledClick = false;
              this.toastService.show({
                type: ToastTypeEnum.ERROR,
                header: 'Failed to delete a comment',
                body: `There was an error in deleting the comment.<br/>${err.error.message}`,
                delay: 5000
              });
            }});
          this.subscriptions.add(deleteDocumentComment$);
      }
    }, () => {});
  }

  onSaveGeneralComment(event){
    this.isDisabledClick = true;
    const payload: DocumentAnnotation = {
      end_pos: 0,
      field: 'general',
      publication_number: this.publicationNumber || null,
      start_pos: 0,
      source: DocumentCommentSourceEnum.GENERAL,
      text: '',
      color: '',
      comment: event.comment,
      private: event.private,
      parent_comment_id: event.parent_comment_id ? event.parent_comment_id : null,
      tagged_user_ids: event.tagged_user_ids,
      tagged_group_ids: event.tagged_group_ids
    };
    const saveDocumentComment$ = this.annotationService.saveDocumentComment(payload, Number(this.patent.general.docdb_family_id))
        .pipe(finalize(() => {this.isDisabledClick = false;this.onCancelComment();})).subscribe({next:({data}) => {
          data.user = this.userService.getUser().profile;
          this.annotationStore.addComment(data);
          this.annotationService.highlightComments(true, data);
        }, error: err => {
          console.error(err);
          this.toastService.show({
            type: ToastTypeEnum.ERROR,
            header: 'Failed to save a comment',
            body: `There was an error in saving the comment.<br/>${err.error.message}`,
            delay: 5000
          });
        }});
      this.subscriptions.add(saveDocumentComment$);
  }

  onCancelComment(){
    document.body.click();
  }

  sortComments(sortBy){
    this.annotationService.commentsSortBy = sortBy;
    this.annotationService.sortAndFilterComments();
  }

  filterComments(option: TreeFilterOption, filterPopover: NgbPopover, childFilterPopover: NgbPopover){
    this.closeOtherOpeningFilterPopovers(option);

    if (option.hasChildren) {
      childFilterPopover.toggle();
      return;
    }

    this.annotationService.selectCommentsFilterOption(option);

    filterPopover.close();
    this.clearOpeningFilterPopovers();
    this.annotationService.sortAndFilterComments();
    this.setSelectedTabByComments();
  }

  getSelectedCommentsFilterOptionsTooltip() {
    const selectedOptions = this.annotationService.findSelectedCommentsFilterOptions();
    if (selectedOptions.length < 1 || this.annotationService.parentOfSelectedFilterOption.isToggle) {
      return null;
    }

    const tooltip = [];

    for (let op of selectedOptions.filter(o => o.itemType === TreeFilterItemType.TEAM_USER)) {
      tooltip.push(this.userTitlePipe.transform(op.item as TeamUser));
    }

    const tooltipText = tooltip.length <= 2 ? tooltip.join(' and ') : tooltip.slice(0, -1).join(', ') + ' and ' + tooltip[tooltip.length - 1];
    return `Filter by ${tooltipText}`;
  }

  onMouseEnterFilterOption(option: TreeFilterOption, childFilterPopover: NgbPopover) {
    if (childFilterPopover) {
      this.closeOtherOpeningFilterPopovers(option);
      this.trackingOpenedFilterPopovers(option, childFilterPopover);
      childFilterPopover.open();
    }
  }

  private trackingOpenedFilterPopovers(option: TreeFilterOption, filterPopover: NgbPopover) {
    if (option.hasChildren) {
      const isExisting = this.openingFilterPopovers.find((p) => this.annotationService.isTreeFilterOptionEqual(p.filterOption, option));
      if (!isExisting) {
        this.openingFilterPopovers.push({
          filterOption: option,
          popover: filterPopover
        });
      }
      this.maxPopoverDepth = option.depth;
    }
  }

  onCommentsFilterPopoverClosed() {
    this.clearOpeningFilterPopovers();
  }

  private clearOpeningFilterPopovers() {
    this.openingFilterPopovers = [];
    this.maxPopoverDepth = 0;
  }

  private closeOtherOpeningFilterPopovers(option: TreeFilterOption) {
    if (this.maxPopoverDepth >= option.depth) {
      this.openingFilterPopovers.forEach((p) => {
        if (p.filterOption.depth >= option.depth && !this.annotationService.isTreeFilterOptionEqual(p.filterOption, option)) {
          p.popover.close();
        }
      });
      this.openingFilterPopovers = this.openingFilterPopovers.filter((p) => p.filterOption.depth < option.depth);
    }
  }

  private setSelectedTabByComments() {
    const g = this.generalComments?.length || 0;
    const s = this.sectionComments?.length || 0;
    if ((g === 0 || s === 0) && (g !== s)) {
      this.setSelectedTab(g > 0 ? PatentCommentsSelectedTabEnum.GENERAL : PatentCommentsSelectedTabEnum.SECTION);
    }
  }
}
