import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  CollaborationService,
  Collection,
  CollectionService,
  CollectionViewTypeEnum,
  ConfirmationDialogService,
  Folder,
  NotificationsService,
  PaginationMetadata,
  TagService,
  ToastService,
  UserService
} from '@core/services';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { finalize, take } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { ShareDialogComponent, ShareDialogResult } from '@shared/components';
import { FolderFormComponent } from '@collections/folder-form/folder-form.component';
import { CollectionFormComponent } from '@collections/collection-form/collection-form.component';
import {
  CollectionsCategory,
  FilterOperatorEnum,
  FilterOperatorTypeEnum,
  FilterParam,
  isCategoryNotDefined,
  showIntermediateFolders,
  SlideInOutAnimations
} from '@collections/shared/types';
import {
  Collaboration,
  CollaborationPermissionEnum,
  CollaborationResourceTypeEnum,
  CollaborationStatusEnum
} from '@core/models';
import { AggregationComponent } from '@collections/aggregation/aggregation.component';
import { CollectionStoreService } from '@core/store';
import { TagModel } from '@core/models/tag.model';

enum TagFilterOption {
  ALL = 'All',
  CREATED_BY_ME = 'Created by me',
  MY_PRIVATE_TAGS = 'My private tags',
  ONLY_PUBLIC_TAGS = 'Only public tags'
}

@Component({
  selector: 'app-collections',
  templateUrl: './collections.component.html',
  styleUrls: ['./collections.component.scss'],
  animations: SlideInOutAnimations
})
export class CollectionsComponent implements OnInit, OnDestroy {
  loading = false;
  loadingSharedCollaborations: boolean;
  errors: Array<string> = [];
  showNotFound: boolean;
  successMessage: string;
  pagination: PaginationMetadata;
  sharedPagination: PaginationMetadata;
  tags: TagModel[];
  category: CollectionsCategory = CollectionsCategory.DEFAULT;
  currentFolderId: number = null;
  currentFolder: Folder;
  isSharedFolderId: boolean;
  currentCollectionId: number = null;
  selectedCollections: Collection[] = [];
  filterByNameParam = new FilterParam('name', FilterOperatorEnum.TEXT_EQ, '', FilterOperatorTypeEnum.TEXT);
  filterByCreatedAtParam = new FilterParam('created_at', FilterOperatorEnum.COMPARABLE_EQ, '',
    FilterOperatorTypeEnum.DATE);
  filterByUpdatedAtParam = new FilterParam('updated_at', FilterOperatorEnum.COMPARABLE_EQ, '',
    FilterOperatorTypeEnum.DATE);
  filterByResultsCountParam = new FilterParam('results_count', FilterOperatorEnum.COMPARABLE_EQ, '',
    FilterOperatorTypeEnum.NUMBER);

  collectionsSharedWithMe: Collection[];
  foldersSharedWithMe: Folder[];

  countCollectionsSharedWithMe: number;
  countFoldersSharedWithMe: number;

  isLoadingFolders = false;
  filter = '';
  sortOrder = 'asc';
  sortBy: string = 'name';
  listSort = [{label: 'Alphabetically', value: 'name'},
    {label: 'Recently added', value: 'created_at'},
    {label: 'Latest updated', value: 'updated_at'}];

  filterOptions = [
    TagFilterOption.ALL, TagFilterOption.CREATED_BY_ME, TagFilterOption.MY_PRIVATE_TAGS, TagFilterOption.ONLY_PUBLIC_TAGS
  ];
  filterBy: TagFilterOption = TagFilterOption.ALL;
  /**
   * reference for event of share a specific collection form collection card
   */
  shareCollection: Collection;

  /**
   * reference for filter term to the Annotated documents and Opened documents
   */
  filterTerm = '';

  collectionsCategory = CollectionsCategory;

  private debounce: any;
  isFiltering: boolean;
  private filterParams = [
    this.filterByNameParam, this.filterByCreatedAtParam, this.filterByUpdatedAtParam, this.filterByResultsCountParam
  ];
  private subscriptions = new Subscription();
  private readonly foldersPageSize = 100;
  private readonly collectionsPageSize = 24;
  private readonly collectionsRootPath = 'collections';
  private readonly collectionPath = 'collection';

  constructor(
    public activeModal: NgbActiveModal,
    public userService: UserService,
    private activatedRoute: ActivatedRoute,
    private modalService: NgbModal,
    private toastService: ToastService,
    public collectionService: CollectionService,
    private collaborationService: CollaborationService,
    private notificationsService: NotificationsService,
    public collectionStoreService: CollectionStoreService,
    private confirmationDialogService: ConfirmationDialogService,
    private router: Router,
    public tagService: TagService
  ) {
  }

  get alertMessage(): string {
    if (this.isTaggedPage && this.collections.length === 0) {
      if(this.filter.trim().length >0){
        return 'No tag match your search filter.';
      }
      if (this.userService.hasTagFeature()) {
        return `You have no tags assigned to patent documents.<br>Start managing your tags <a href="/tags">here</a>`;
      }
      return 'All tags that are shared with you will be displayed here. You don’t have the rights to create tags.';
    }
    if (this.currentFolder) {
      if (this.isDefaultFolder) {
        if (this.collections.length === 0 && this.folders.length === 0) {
          return 'This folder does not have any lists yet.';
        }
      } else if (this.isFolderSharedWithMe) {
        if (this.collections.length === 0 && this.countFoldersSharedWithMe === 0) {
          return 'This folder does not have any lists yet.';
        }
      } else {
        if (this.collections.length === 0) {
          return 'This folder does not have any lists yet.';
        }
      }
    }
    if (this.folders?.length === 0 && this.collections?.length === 0 && !this.isSharedFolder) {
        if(this.filter.trim().length >0){
          return 'No list match your search filter.';
        } else {
          return 'You do not have any lists yet.';
        }
    }
    return null;
  }

  get sharedContentAlertMessage(): string {
    if (!this.currentFolder && !this.isTaggedPage && this.filter.trim().length >0 && this.foldersSharedWithMe?.length === 0 && this.collectionsSharedWithMe?.length === 0) {
      return 'No shared list match your search filter.';
    }
    return null;
  }

  get isSharedFolder(): boolean {
    return this.isFolderSharedWithMe;
  }

  get canWriteCurrentFolder() {
    if (!this.currentFolderId) {
      return true;
    }
    return this.canWriteFolder(this.currentFolder)
  }

  canWriteFolder(folder: Folder): boolean {
    return folder?.permissions?.includes('write');
  }

  get isFolderSharedWithMe(): boolean {
    return this.category === CollectionsCategory.SHARED_WITH_ME;
  }

  get isTaggedPage(): boolean {
    return this.category === CollectionsCategory.TAGGED
  }

  get isDefaultFolder(): boolean {
    return this.category === CollectionsCategory.DEFAULT && !!this.currentFolderId;
  }

  get isHomePage(): boolean {
    return !this.currentFolderId && (!this.category || this.category === CollectionsCategory.DEFAULT);
  }

  get folders(): Folder[] {
    return this.collectionStoreService.folders;
  }

  set folders(val: Folder[]) {
    this.collectionStoreService.folders = val;
  }

  get collections(): Collection[] {
    return this.collectionStoreService.collections;
  }

  set collections(val: Collection[]) {
    this.collectionStoreService.collections = val;
  }

  get displayedCollections(): Collection[] {
    return this.collections;
  }

  get collectionsTab(): string {
    return this.collectionStoreService.collectionsTab;
  }

  set collectionsTab(value: string) {
    this.collectionStoreService.collectionsTab = value;
  }

  get paginatingInformation(): string {
    const countExtraDisplayItems = (this.folders?.length || 0)
    const countTotalItems = (this.pagination?.total_hits || this.collections?.length || 0) + countExtraDisplayItems;

    const currentPage = (this.pagination?.current_page ?? 1) - 1;
    const countExtra = (currentPage > 0 ? countExtraDisplayItems : 0);
    const countPreviousItems = countExtra + currentPage * this.collectionsPageSize;
    const startItems = countPreviousItems + 1;
    const endItems = (currentPage > 0 ? countPreviousItems : countExtraDisplayItems) + (this.collections?.length || 0);
    return `Showing ${startItems} - ${endItems} of ${countTotalItems}`;
  }
  get sharedPaginationInformation(){
    const countExtraDisplayItems = this.foldersSharedWithMe?.length || 0;
    const countTotalItems = (this.sharedPagination?.total_hits || this.collectionsSharedWithMe?.length || 0) + countExtraDisplayItems;

    const currentPage = (this.sharedPagination?.current_page ?? 1) - 1;
    const countExtra = (currentPage > 0 ? countExtraDisplayItems : 0);
    const countPreviousItems = countExtra + currentPage * this.collectionsPageSize;
    const startItems = countPreviousItems + 1;
    const endItems = (currentPage > 0 ? countPreviousItems : countExtraDisplayItems) + (this.collectionsSharedWithMe?.length || 0);
    return `Showing ${startItems} - ${endItems} of ${countTotalItems}`;
  }

  get hideShareContent(): boolean{
    return !this.hasWorkflowFeature() || !!this.currentFolderId || this.isTaggedPage ||
      (this.filter.length === 0 && this.countFoldersSharedWithMe === 0 && this.countCollectionsSharedWithMe === 0);
  }

  get pageTitle(): string{
    switch(true){
      case this.isSharedFolderId:
        return 'Shared with me';
      case this.isTaggedPage:
        return 'Tagged documents';
      default :
        return 'My collections';
    }
  }

  isCollectionResourceType(c: Collaboration): boolean {
    return c.resource_type === CollaborationResourceTypeEnum.COLLECTION;
  }

  ngOnInit() {
    this.collections = [];
    this.folders = [];
    const url = this.router.url;
    this.collectionsTab = url.indexOf('tagged') === -1 ? CollectionViewTypeEnum.COLLECTIONS_VIEW : CollectionViewTypeEnum.TAGGED_VIEW;
    const category = this.activatedRoute.snapshot.params['category'];
    this.category = category && !isCategoryNotDefined(category as CollectionsCategory) ? category as CollectionsCategory : CollectionsCategory.DEFAULT;
    if(this.isTaggedPage){
      this.loadTaggedCollection();
      return;
    }
    if (url.indexOf('documents') > -1) {
      this.collectionsTab = url.split('/')[3];
      return;
    }
    const currentCollectionId$ = this.collectionStoreService.currentCollectionId.subscribe({
      next: val => {
        this.currentCollectionId = val;
      }
    });
    this.subscriptions.add(currentCollectionId$);

    const refreshCollections$ = this.collectionStoreService.refreshCollections.subscribe({
      next: val => {
        if (val) {
          this.loadCollections();
        }
      }
    });
    this.subscriptions.add(refreshCollections$);

    const refreshFolders$ = this.collectionStoreService.refreshFolders.subscribe({
      next: val => {
        if (val) {
          this.folders = [];
          this.loadCurrentFolder();
          this.loadFolders();
          this.loadFoldersSharedWithMe();
        }
      }
    });
    this.subscriptions.add(refreshFolders$);

    const params$ = this.activatedRoute.params.subscribe({
      next: params => {
        this.category = params.category as CollectionsCategory || CollectionsCategory.DEFAULT;
        if (this.shouldBackwardCompatibility(params.category, params.folder_id)) {
          return;
        }
        this.currentFolderId = params.folder_id ? Number(params.folder_id) : null;
        this.isSharedFolderId = false;

        this.loadCollectionData();
        this.loadSharedData();
      }
    });
    this.subscriptions.add(params$);
  }

  loadCollectionData(){
    if(this.isTaggedPage){
      this.loadTaggedCollection();
      return;
    }

    if (this.currentFolderId) {
      this.loading = true;
      this.loadCurrentFolder(true);
      this.loadFolders();
    } else {
      this.loadFolders();
      this.loadCollections();
    }
  }

  loadSharedData(){
    this.loadingSharedCollaborations = true;
    this.loadFoldersSharedWithMe();
    this.loadCollectionsSharedWithMe();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.clearStoredCollectionData();
    this.toastService.clear();
  }

  getSortLabel() {
    return this.listSort.find(item => item.value === this.sortBy).label;
  }

  onSortBy(val: string) {
    this.beforeLoadingCollections();
    this.sortBy = val;
    this.filterCollections();
  }

  onSortOrder(val: string) {
    if (this.sortOrder !== val) {
      this.beforeLoadingCollections();
      this.sortOrder = val;
      this.filterCollections();
    }
  }

  navigate(page: number) {
    this.pagination.current_page = page;
    this.loadCollectionData();
  }
  navigateShared(page: number) {
    this.sharedPagination.current_page = page;
    this.loadSharedData();
  }

  onChangeFilter() {
    if (this.debounce) {
      clearTimeout(this.debounce);
    }
    this.isFiltering = true;
    this.debounce = setTimeout(() => {
      this.isFiltering = false;
      this.filterCollections();
    }, 300);
  }

  filterCollections() {
    this.beforeLoadingCollections();
    if (this.isTaggedPage) {
      this.loadTaggedCollection();
    } else {
      this.loadCollections();
      this.loadFolders();
      this.loadFoldersSharedWithMe();
      this.loadCollectionsSharedWithMe();
    }
  }

  backToCollections() {
    const commands = [this.collectionsRootPath];

    if (this.currentFolder?.parent_id) {
      commands.push(this.category);
      commands.push(this.currentFolder.parent_id.toString());
    } else {
      if (this.currentFolderId && showIntermediateFolders(this.category)) {
        commands.push(this.category);
      }
    }

    this.router.navigate(commands).then();
  }

  createNewFolder() {
    this.modalService.dismissAll();
    const modal = this.modalService.open(FolderFormComponent, {size: 'lg'});
    modal.componentInstance.parentFolder = this.currentFolder;
  }

  userOwnCollection(collection: Collection): boolean {
    return this.userService.isResourceOwner(collection?.user_id);
  }

  onCollectionSelected(selected: boolean, c: Collection) {
    if (selected) {
      this.selectedCollections.push(c);
    } else {
      this.selectedCollections = this.selectedCollections.filter(o => o.id !== c.id);
    }
  }

  onCollectionDeleted(deleted: boolean, c: Collection) {
    if (deleted) {
      this.updateDeletedCollection(c, true);
    }
  }

  onDeleteSelectedClick() {
    if (this.selectedCollections && this.selectedCollections.length > 0) {
      const subject = this.selectedCollections.length > 1 ? 'lists' : 'list';
      const title = '<i class="fas fa-trash-alt fa-2x"></i>';
      const message = `<div class="text-center">Are you sure you want to <b>delete</b> <br/><span class="text-green">${this.selectedCollections.length} selected ${subject}</span> ?</div>`;

      const modalRef = this.confirmationDialogService.confirm(title, message, 'Yes', 'Cancel', 'lg');

      modalRef.then(val => {
        if (val) {
          const len = this.selectedCollections.length;
          let count = 0;
          this.loading = true;
          for (const c of this.selectedCollections) {
            const deleteCollection$ = this.collectionService.deleteCollection(c.id).pipe(take(1))
              .pipe(finalize(() => {
                count += 1;
                this.updateDeletedCollection(c, count === len);
              }))
              .subscribe({ next:_ => { }, error:error => { console.error(error); }});
            this.subscriptions.add(deleteCollection$);
          }

          this.selectedCollections = [];
        }
      }, rejected => {
      });
    }
  }

  isCollectionSelected(c: Collection) {
    return c && this.selectedCollections.findIndex(o => o.id === c.id) > -1;
  }

  createNewCollection() {
    this.modalService.dismissAll();
    const modal = this.modalService.open(CollectionFormComponent, {size: 'xl'});
    modal.componentInstance.folder = this.currentFolder;
  }

  onFolderDeleted(event: boolean, f: Folder) {
    if (event) {
      this.folders = this.folders.filter(o => o.id !== f.id);
      this.foldersSharedWithMe = this.foldersSharedWithMe.filter(o => o.id !== f.id);
    }
  }

  onCombineSelectedClick() {
    if (this.selectedCollections && this.selectedCollections.length > 1) {
      const modal = this.modalService.open(AggregationComponent, {size: 'lg', windowClass: 'aggregate-collections'});
      if (this.isDefaultFolder) {
        modal.componentInstance.folderId = this.currentFolderId;
      }
      modal.componentInstance.selectedCollections = this.selectedCollections;
      modal.result.then(value => {
        this.selectedCollections = [];
      }, reason => {
      });
    }
  }

  onClearSelectedCollectionsClicked() {
    this.selectedCollections = [];
  }

  /**
   * onShareCollection
   *
   * Action listener for event 'share collection' from app-share-collection component
   * @param event response of submitting share collection from
   */
  onShareCollection(event: Collection, collectionId) {
    if (event && 'share_code' in event) {
      const foundIndex = this.collections.findIndex(c => c.id === event.id);
      this.collections[foundIndex] = {...this.collections[foundIndex], ...event};
      this.shareCollection = null;
    } else {
      const collection = this.collections.find(o => o.id === collectionId);
      this.loadUsersSharedByMe([collection]);
      this.loadGroupsSharedByMe([collection]);
    }
  }
  onShareFolder(folder: Folder){
    const modal = this.modalService.open(ShareDialogComponent, {size: 'lg'});
    modal.componentInstance.headline = 'Share folder';
    modal.componentInstance.hideAgreementCheck = true;
    modal.componentInstance.nameReadOnly = true;
    modal.componentInstance.resourceId = folder.id;
    modal.componentInstance.resourceType = CollaborationResourceTypeEnum.FOLDER;
    if(!this.userService.isResourceOwner(folder.user_id) && folder.collaboration){
      modal.componentInstance.permission = folder.collaboration.permission;
    } else {
      modal.componentInstance.permission = CollaborationPermissionEnum.READONLY;
      modal.componentInstance.showPermissionSwitch = true;
    }
    modal.componentInstance.resource = folder;
    modal.componentInstance.canSwitchShareType = false;
    modal.componentInstance.searchService = this.collectionService;
    modal.componentInstance.storeService = this.collectionStoreService;

    modal.result.then((data: ShareDialogResult) => {
      if(this.userService.isResourceOwner(folder.user_id)){
        const index = this.folders.findIndex(o => o.id === folder.id);
        this.folders[index].collaborators.users = data.shared_users;
        this.folders[index].collaborators.groups = data.shared_groups;
      }
      this.collectionStoreService.setSaveToCollectionSuccess(null);
    }, reason => { });
  }

  onOpenShareCollectionForm(collection: Collection, collaboration?: Collaboration) {
    if (!this.userService.canUseSaveAndShareFeature('share') || this.collectionService.isCollectionSharedViaFolder(collection)) {
      return;
    }
    this.shareCollection = collection;

    const modal = this.modalService.open(ShareDialogComponent, {size: 'lg'});
    modal.componentInstance.headline = this.userOwnCollection(collection)?'Share your collection':'Share collection';
    modal.componentInstance.hideAgreementCheck = true;
    modal.componentInstance.nameReadOnly = true;
    modal.componentInstance.resourceId = this.shareCollection.id;
    modal.componentInstance.resourceType = CollaborationResourceTypeEnum.COLLECTION;
    modal.componentInstance.resource = collection;
    if(collaboration){
      modal.componentInstance.permission = collaboration.permission;
    } else if(this.isSharedFolderId && collection.collaboration){
      modal.componentInstance.permission = collection.collaboration.permission;
    }
    modal.componentInstance.canSwitchShareType = this.userOwnCollection(collection);
    modal.componentInstance.searchService = this.collectionService;
    modal.componentInstance.storeService = this.collectionStoreService;

    modal.result.then((data: ShareDialogResult) => {
      this.onShareCollection(data.collection, this.shareCollection.id);
      this.collectionStoreService.setSaveToCollectionSuccess(null);
    }, reason => {
      this.onShareCollection(null, this.shareCollection.id);
      this.collectionStoreService.setSaveToCollectionSuccess(null);
    });
  }

  hasWorkflowFeature(): boolean {
    return this.userService.canUseWorkflowFeature();
  }

  getTitle(): string {
    return this.currentFolder?.name;
  }

  onChangeTab(tab: CollectionViewTypeEnum): void {
    const urls  = {
      [CollectionViewTypeEnum.COLLECTIONS_VIEW]: this.collectionStoreService.currentCollectionsViewUrl ? this.collectionStoreService.currentCollectionsViewUrl : '/collections',
      [CollectionViewTypeEnum.ANNOTATED_VIEW]: '/collections/documents/annotated',
      [CollectionViewTypeEnum.OPENED_DOCUMENTS_VIEW]: '/collections/documents/opened',
      [CollectionViewTypeEnum.TAGGED_VIEW]: this.buildFolderLink(null, CollectionsCategory.TAGGED)
    };

    if (this.collectionsTab === CollectionViewTypeEnum.COLLECTIONS_VIEW) {
      this.collectionStoreService.currentCollectionsViewUrl = this.router.url;
      this.clearPageData();
    }

    this.router.navigate([urls[tab]]).then(() => {
      this.collectionsTab = tab;
    });
  }

  buildFolderLink(folderId: number, category: CollectionsCategory) {
    const categoryPath = this.getCategoryPath(category);
    if (folderId) {
      return `${categoryPath}/${folderId}`;
    }
    return categoryPath;
  }

  buildCollectionLink(collectionId: number, category: CollectionsCategory) {
    const categoryPath = this.getCategoryPath(category);
    if (this.currentFolderId) {
      return `${categoryPath}/${this.currentFolderId}/${this.collectionPath}/${collectionId}`;
    }
    return `${categoryPath}/${this.collectionPath}/${collectionId}`;
  }

  private getCategoryPath(category: CollectionsCategory): string {
    return `/${this.collectionsRootPath}/${category}`;
  }

  private isSharedCollection(collectionId, collaboration: Collaboration): boolean {
    return collaboration.status !== CollaborationStatusEnum.UNSHARED &&
      collaboration.resource_id === collectionId && this.isCollectionResourceType(collaboration);
  }

  private beforeLoadingCollections() {
    this.pagination = null;
  }

  private clearStoredCollectionData() {
    this.collectionsSharedWithMe = [];
    this.foldersSharedWithMe = [];
    this.collectionStoreService.setCurrentCollectionId(null);
    this.collectionStoreService.setRefreshCollections(false);
    this.collectionStoreService.setRefreshFolders(false);
  }

  private initFilterParams() {
    this.filterByNameParam.value = '';
    this.filterByCreatedAtParam.value = null;
    this.filterByUpdatedAtParam.value = null;
    this.filterByResultsCountParam.value = null;
  }

  private updateDeletedCollection(c: Collection, isRefreshCollections: boolean) {
    const pagination = this.pagination;
    this.collections = this.collections.filter(item => item.id !== c.id);

    if (isRefreshCollections && this.collections.length === 0 &&
      pagination?.current_page > 1 && pagination?.current_page === pagination?.last_page) {
      pagination.current_page -= 1;
    }

    if (isRefreshCollections) {
      this.loadCollections();
    }
  }

  private buildPayload(page: number, pageSize: number) {
    const payload = {
      page: page,
      page_size: pageSize,
      sort_by: this.sortBy,
      sort_order: this.sortOrder
    };

    if (this.currentFolderId) {
      payload['folder_id'] = this.currentFolderId;
    } else {
      payload['folder_id'] = 'is:null';
    }
    payload['exclude_monitor_runs'] = 1;
    payload['exclude_tagged_documents'] = 1;


    if (this.filter) {
      payload['name'] = `like:%${this.filter.trim()}%`;
    }

    for (const filterParam of this.filterParams) {
      const query = filterParam ? filterParam.buildQuery() : null;
      if (query) {
        payload[filterParam.name] = query;
      }
    }

    if (this.isTaggedPage) {
      const userId = this.userService.getUser()?.profile?.id;
      switch (this.filterBy) {
        case TagFilterOption.CREATED_BY_ME:
          payload['user_id'] = userId;
          break;
        case TagFilterOption.MY_PRIVATE_TAGS:
          payload['user_id'] = userId;
          payload['private'] = 1;
          break;
        case TagFilterOption.ONLY_PUBLIC_TAGS:
          payload['private'] = 0;
          break;
      }
    }

    return payload;
  }

  private loadTaggedCollection(){
    this.loading = true;
    this.tags = [];

    const page = this.pagination?.current_page ?? 1;
    const payload = this.buildPayload(page, this.collectionsPageSize);
    const getTags$ = this.tagService.getTags(payload)
      .pipe(
        take(1),
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: (data) => {
          this.tags = data.tags;
          this.pagination = data.page;
          const userIds = [...new Set(this.tags.map(a => a.user_id))];
          if (userIds.length) {
            const teamUsersPayload = {id: 'in:' + userIds.join(','), 'load_all': 1, include_me: 1, include_blocked: 1};
            const getTeamUsers$ = this.userService.getTeamUsers(teamUsersPayload)
              .pipe(take(1))
              .subscribe({
                next: ({page, users}) => {
                  data.tags.forEach(t => {
                    t['user'] = users.find(u => u.id === t.user_id);
                  });
                  this.tags = data.tags;
                }, error: error => {
                }
              });
            this.subscriptions.add(getTeamUsers$);
          }
        }
      });
    this.subscriptions.add(getTags$);
  }

  private loadCollections() {
    // if (this.isSharedFolder) { return; }
    this.loading = this.currentFolderId ? true : !(this.collections?.length > 0);

    const page = this.pagination?.current_page ?? 1;
    const payload = this.buildPayload(page, this.collectionsPageSize);
    let ob
    if(this.isSharedFolderId){
      ob = this.collectionService.getSharedCollections(payload);
    } else {
      ob = this.collectionService.getCollections(payload);
    }

    const getCollections$ = ob.pipe(take(1), finalize(() => this.loading = false)).subscribe({
        next:(data) => {
        this.pagination = data.page;
        this.collections = data.result_collections;
        if(this.isSharedFolderId){
          this.appendSharedByUsers(this.collections);
        } else {
          this.loadUsersSharedByMe(this.collections);
          this.loadGroupsSharedByMe(this.collections);
        }
        this.loadFolderCollaborations(this.collections);
      },
      error: (error) => {
        this.collections = [];
        console.error(error);
      }
    });
    this.subscriptions.add(getCollections$);
  }

  private appendSharedByUsers(collections: Collection[]){
    const userIds = [...new Set(collections.map(c => c.collaboration.shared_by_id))];
    if (userIds.length) {
      const teamUsersPayload = {id: 'in:' + userIds.join(','), 'load_all': 1, include_me: 1, include_blocked: 1};
      const getTeamUsers$ = this.userService.getTeamUsers(teamUsersPayload).subscribe({
        next: ({page, users}) => {
          collections.forEach(c => {
            c.collaboration['shared_by'] = users.find(u => u.id === c.collaboration.shared_by_id);
          });
        }, error: error => {
        }
      });
      this.subscriptions.add(getTeamUsers$);
    }
  }

  private loadFolders() {
    this.isLoadingFolders = !(this.folders?.length > 0);

    const payload = this.buildPayload(1, this.foldersPageSize);
    if(payload['sort_by'] === 'expires_at'){
      delete payload['sort_by'];
    }
    const getFolders$ = this.collectionService.getFolders(payload)
      .pipe(
        take(1),
        finalize(() => this.isLoadingFolders = false)
      )
      .subscribe({
        next: ({folders, page}) => {
          this.folders = folders;
        }, error: error => {
          this.folders = [];
          console.error(error);
        }
      });
    this.subscriptions.add(getFolders$);
  }

  private loadCurrentFolder(loadCollection?: boolean) {
    if (this.currentFolderId) {
      const getFolder$ = this.collectionService.getFolder(this.currentFolderId)
        .pipe(take(1))
        .subscribe({
          next: (folder) => {
            this.currentFolder = folder;
            if(loadCollection){
              this.isSharedFolderId = !this.userService.isResourceOwner(folder.user_id);
              this.loadCollections();
            }
            this.shouldGoToSharedWithMeCategory();
          }, error: error => {
            console.error(error);
            this.currentFolder = null;
            if(error.status === 404){
              this.showNotFound = true;
            }
          }
        });
      this.subscriptions.add(getFolder$);
    } else {
      this.currentFolder = null;
    }
  }

  private loadFoldersSharedWithMe(){
    if (!this.isHomePage && !this.isFolderSharedWithMe) {
      return;
    }
    const payload = this.buildPayload(1, this.collectionsPageSize);

    if(payload['sort_by'] === 'expires_at'){
      delete payload['sort_by'];
    }
    const getCollections$ = this.collectionService.getShareFolders(payload)
      .pipe(finalize(() => {this.loading = false; this.loadingSharedCollaborations = false;})).subscribe({
          next:(data) => {
          this.countFoldersSharedWithMe = data.folders.length;
          this.foldersSharedWithMe = data.folders;
        },
        error: (error) => {
          this.foldersSharedWithMe = [];
          console.error(error);
        }
      });
    this.subscriptions.add(getCollections$);
  }

  private loadCollectionsSharedWithMe() {
    if (!this.isHomePage) {
      return;
    }
    const page = this.sharedPagination?.current_page ?? 1;
    const payload = this.buildPayload(page, this.collectionsPageSize);
    payload['creation_mode'] = 'DEFAULT';
    this.subscriptions.add(this.collectionService.getSharedCollections(payload)
      .pipe(finalize(() => {this.loading = false; this.loadingSharedCollaborations = false;})).subscribe({
          next:(data) => {
            this.sharedPagination = data.page
            this.collectionsSharedWithMe = data.result_collections;
            this.appendSharedByUsers(this.collectionsSharedWithMe);
            this.countCollectionsSharedWithMe = data?.page?.total_hits ?? 0;
          },
          error: (error) => {
            this.collectionsSharedWithMe = [];
            console.error(error);
          }}));
  }

  private loadUsersSharedByMe(collections: Array<Collection>) {
    if (this.hasWorkflowFeature()) {
      const collectionIds = collections.filter((o) => o && o.id).map(o => o.id);
      const getUsers$ = this.collaborationService.getUsers(collectionIds, CollaborationResourceTypeEnum.COLLECTION)
        .pipe(take(1))
        .subscribe({
          next: (users) => {
            for (const c of collections) {
              if (c) {
                c.users = users.filter(u => this.isSharedCollection(c.id, u.collaboration));
              }
            }
          }
        });
      this.subscriptions.add(getUsers$);
    }
  }

  private loadFolderCollaborations(collections: Array<Collection>){
    if (this.hasWorkflowFeature() && this.currentFolder?.id) {
      this.subscriptions.add(this.collaborationService.getCollaboration(this.currentFolder.id, CollaborationResourceTypeEnum.FOLDER).subscribe({
          next: (data) => {
            if(data.users.length> 0 || data.groups.length> 0 ) {
              for (const c of collections) {
                if (c) {
                  c.folder_collaboration = data;
                }
              }
              if(this.isSharedFolderId){
                this.collaborationService.markAsRead(this.currentFolder.id, CollaborationResourceTypeEnum.FOLDER).subscribe();
                this.notificationsService.markAsReadForResource(this.currentFolder.id, CollaborationResourceTypeEnum.FOLDER).subscribe();
              }
            }
          }
        }));
    }
  }

  private loadGroupsSharedByMe(collections: Array<Collection>) {
    if (this.hasWorkflowFeature()) {
      const collectionIds = collections.filter((o) => o && o.id).map(o => o.id);
      const getGroups$ = this.collaborationService.getGroups(collectionIds, CollaborationResourceTypeEnum.COLLECTION)
        .pipe(take(1))
        .subscribe({
          next: (groups) => {
            for (const c of collections) {
              if (c) {
                c.groups = groups.filter(u => this.isSharedCollection(c.id, u.collaboration));
              }
            }
          }
        });
      this.subscriptions.add(getGroups$);
    }
  }

  private clearPageData() {
    this.collections = [];
    this.folders = [];
    this.foldersSharedWithMe = [];
    this.countFoldersSharedWithMe = 0;
    this.collectionsSharedWithMe = [];
    this.countCollectionsSharedWithMe = 0;
  }

  private shouldBackwardCompatibility(category: string, folderId: string): boolean {
    // To backward compatibility with direct url such as /collections/1
    const collectionId = Number(category);
    if (collectionId && Number.isInteger(collectionId)) {
      this.router.navigate([this.collectionsRootPath, CollectionsCategory.DEFAULT, this.collectionPath, collectionId]);
      return true;
    }

    // To backward compatibility with direct url such as /collections/folders/1
    if (isCategoryNotDefined(category as CollectionsCategory) && folderId) {
      this.router.navigate([this.collectionsRootPath, CollectionsCategory.DEFAULT, folderId]);
      return true;
    }

    return false;
  }

  private shouldGoToSharedWithMeCategory() {
    if (this.currentFolder && !this.userService.isResourceOwner(this.currentFolder.user_id)) {
      this.router.navigate([this.collectionsRootPath, CollectionsCategory.SHARED_WITH_ME, this.currentFolder.id]);
    }
  }

  onTagDeleted() {
    this.loadTaggedCollection();
  }

  onFilterBy(option: TagFilterOption) {
    this.filterBy = option;
    this.pagination.current_page = 1;
    this.loadTaggedCollection();
  }
}
