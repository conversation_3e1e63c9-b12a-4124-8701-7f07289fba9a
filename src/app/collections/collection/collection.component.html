<div class="d-flex flex-column justify-content-start min-vh-100">
    <app-header></app-header>

    <app-spinner *ngIf="isLoadingCollection"></app-spinner>

    <div class="flex-fill">
      <ng-container *ngIf="collection || (isTasksMode && showTasks)">
        <div *ngIf="showTasks else collectionContentTemplate"
             class="collection-content-container collection-show-tasks">
          <div *ngIf="expandSidebar" class="m-0 h-100 d-flex justify-content-between align-items-stretch">
            <div class="p-0 flex-fill overflow-auto d-flex">
              <ng-container *ngIf="collection else noTaskSelectedMessageTemplate" [ngTemplateOutlet]="collectionContentTemplate"></ng-container>
            </div>

            <div class="right-side-container sticky-top p-0 m-0">
              <app-task-list [resourceId]="paramCollectionId" [resourceType]="taskResourceTypeEnum.COLLECTION"
                             [showCreateTaskButton]="false" [showDocumentTitle]="true"  [showTaskAnswerInput]="false"
                             (taskSelectedEvent)="onTaskSelected($event)"
                             class="w-100 d-flex flex-column justify-content-between h-100">
              </app-task-list>
            </div>
          </div>

          <div *ngIf="!expandSidebar" class="row m-0 h-100">
            <div class="p-0 col-12">
              <ng-container *ngIf="collection else noTaskSelectedMessageTemplate" [ngTemplateOutlet]="collectionContentTemplate"></ng-container>
            </div>
          </div>

          <ng-container [ngTemplateOutlet]="sidebarTemplate"></ng-container>
        </div>
      </ng-container>

      <div class="container-fluid mt-5 mb-5" *ngIf="collectionNotFound && (!isTasksMode || !showTasks)">
        <app-alert type="warning" mainTitle="Not found !" [hideCloseBtn]="true" message="Requested list could not be found">
        </app-alert>
      </div>
    </div>

    <app-zoom-chart [storeService]="collectionStoreService"></app-zoom-chart>

    <app-footer></app-footer>
</div>

  <ng-template #noTaskSelectedMessageTemplate>
    <div class="m-4">
      <app-alert type="warning" mainTitle="No tasks selected !" [hideCloseBtn]="true"
                 message="Please select a task to view its documents">
      </app-alert>
    </div>
  </ng-template>

  <ng-template #collectionContentTemplate>
    <div class="search-results-container"
      [ngClass]="{'d-inline-block flex-fill ms-0 me-0 ps-3 pe-3 d-flex flex-column align-items-stretch': showTasks && expandSidebar}">
      <div class="collection-info">
        <div class="d-flex w-100" [ngClass]="{'mt-3 container-fluid': !showTasks || !expandSidebar}">
          <div class="collection-info-left p-r-spacing-xxx-big">
            <div class="clt-patents-title content-heading-h4 content-color-primary m-y-spacing-xxx-s" *ngIf="!isTagCollection"><span [ngbTooltip]="collection.name">{{collection.name}}</span></div>
            <app-tag-item *ngIf="isTagCollection && customTag"
              [tag]="customTag"
              [canManageTag]="canManageTag"
              [showOpenTagCollection]="false"
              [size]="'medium'"
              class="mb-3 d-inline-block border-1 border-bold radius-sm">
            </app-tag-item>
            <div class="collection-info-table">
              <div class="clt-patents-desc collection-info-row align-items-start">
                <div class="collection-info-label"><i class="fa-light fa-pen-to-square m-r-spacing-xx-s fa-fw"></i> Description</div>
                <div *ngIf="!editingDescription"
                     class="collection-info-value cursor-pointer w-100 radius-sm d-flex flex-column align-items-start"
                     [class.collection-info-value-disabled]="!collection.description?.trim()?.length"
                     appMouseHover hoverClass="figma-bg-tertiary"
                     (click)="editDescription()" style="white-space: pre-line">
                  <div appTextEllipsis toggleCssClass="ellipsis-text-3" class="ellipsis-text-3">
                    {{ collection.description?.trim()?.length ? collection  .description : 'Empty'}}
                  </div>
                </div>
                <textarea #description class="collection-info-input content-body-small content-color-secondary"
                          maxlength="255"
                          (input)="adjustTextareaHeight()"
                          [(ngModel)]="descriptionText"
                          (blur)="cancelEditingDescription()"
                          (keydown.escape)="cancelEditingDescription()"
                          (keydown.enter)="saveDescription($event)"
                          [hidden]="!editingDescription">
                </textarea>
              </div>
              <div *ngIf="original_pagination" class=" clt-patents-counts collection-info-row">
                <div class="collection-info-label"><i class="fa-light fa-files m-r-spacing-xx-s fa-fw"></i> {{'Patent' | pluralize: collection.collection_type == 'PUBLICATION' ? original_pagination?.total_publications : original_pagination?.total_hits}}</div>
                <div class="collection-info-value">
                  {{original_pagination?.total_hits}}&nbsp;
                  <span *ngIf="collection.collection_type == 'FAMILY'">
                    {{ 'patent family' | pluralize: original_pagination?.total_hits }}
                    ({{original_pagination?.total_publications}} {{ 'publication' | pluralize: original_pagination?.total_publications }})
                  </span>
                  <span *ngIf="collection.collection_type == 'PUBLICATION'">
                    {{ 'patent publication' | pluralize: original_pagination?.total_publications }}
                  </span>
                </div>
              </div>
              <div *ngIf="!isTagCollection && collectionSources" class="clt-patents-sources collection-info-row">
                <div class="collection-info-label"><i class="fa-light fa-print-magnifying-glass m-r-spacing-xx-s fa-fw"></i> Sources</div>
                <div class="collection-info-value" [innerHtml]="collectionSources"></div>
              </div>
              <div *ngIf="collectionCollaborators" class=" clt-patents-collaborators collection-info-row">
                <div class="collection-info-label"><i class="fa-light fa-users m-r-spacing-xx-s fa-fw"></i> Members</div>
                <div class="collection-info-value">
                  <app-user-avatars
                        [users]="collectionCollaborators" [numberDisplayedUsers]="15"
                        [numberDisplayedGroups]="15" [distanceBetweenAvatars]="20" avatarsTooltipPrefix=""
                        class="cursor-pointer">
                  </app-user-avatars>
                </div>
              </div>

              <div class="clt-patents-sources collection-info-row">
                <div class="collection-info-label"><i class="fa-regular fa-calendar m-r-spacing-xx-s fa-fw"></i> Created on</div>
                <div class="collection-info-value">{{collection.created_at | dateFormat: 'ShortDate'}}</div>
              </div>
            </div>
          </div>
          <div class="collection-info-right collection-history" [class.text-end]="!showHistory" *ngIf="collection?.collection_sources?.length && !isTagCollection">
            <div class="show-history-btn button-main-secondary-grey button-small" *ngIf="!showHistory" (click)="showHistory=!showHistory"><i class="fa-regular fa-angles-left"></i> History</div>
            <div class="history-container" *ngIf="showHistory">
              <div class="history-header d-flex justify-content-between p-b-spacing-sm m-b-spacing-sm">
                <div class="flex-grow-1 d-flex"><span class="content-heading-h4 me-2">History</span>
                  <app-tooltip id='history_search'
                    tooltipTitle='These are the events that happened on this list.'
                    tooltipText='These are the events that happened on this list.'
                    tooltipPosition="left" tooltipIconSize="sm"></app-tooltip>
                </div>
                <div class="d-flex">
                  <span class="figma-dropdown history-sort-by">
                    <div class="figma-dropdown-btn button-main-tertiary-grey button-small content-label-small"  appDropdownToggle>
                      <i class="fa-light fa-lg fa-arrow-up-arrow-down"></i> {{historySort}}
                    </div>
                    <div  class="figma-dropdown-content figma-dropdown-content-right radius-big p-y-spacing-big p-x-spacing-md" >
                      <div class="figma-dropdown-label content-heading-h6 content-color-tertiary m-b-spacing-none">Sort by</div>
                      <div class="figma-dropdown-item figma-dropdown-item-hover p-r-spacing-s" *ngFor="let s of historySortOption" (click)="sortHistory(s);" [class.active]="historySort === s">
                        <span class=" p-r-spacing-xxx-big">{{s}} </span>
                      </div>
                    </div>
                  </span>
                  <span class="button-main-tertiary-grey button-small content-label-small button-square"
                  ngbTooltip="Hide history" tooltipClass="white-tooltip" (click)="showHistory=!showHistory">
                    <i class="fa-regular fa-angles-right"></i>
                  </span>
                </div>
              </div>
              <div class="history-table">
                <div *ngFor="let s of collection?.collection_sources; let first = first; let last = last;" class="history-row" [ngClass]="{'not-clickable': s.is_remove_event, 'active-row': isSourceFiltered(s)>-1}" (click)="onFilterSource(s)">
                  <div class="history-table-icon" [class.first-row]="first" [class.last-row]="last">
                  </div>
                  <div class="w-100 d-flex p-y-spacing-md">
                    <div class="history-user-avatar " *ngIf="s.user">
                      <app-user-avatar [user]="s.user" [hasSubTitle]="false" class="collection-source-avatar"></app-user-avatar>
                    </div>
                    <div class="history-description m-l-spacing-x-s">
                      <div class="history-description-text content-body-small">
                        <i class="fa-regular fa-print-magnifying-glass"></i>
                        {{ s.user ? (s.user | userTitle) : s.user_id }}
                        {{s.is_remove_event ? 'removed': 'added'}}
                        <span class="content-heading-h6">{{s.documents_count}} Patent {{ ( collection.collection_type == 'PUBLICATION' ? 'publication' :'family') | pluralize: s.documents_count }} </span>
                        from
                        <ng-container *ngIf="s.is_remove_event;else displaySource">
                          this list.
                        </ng-container>
                        <ng-template #displaySource>
                        <ng-container [ngSwitch]="getSourceType(s)">
                          <span *ngSwitchCase="collectionSourceTypeEnum.MANUAL" class="tag-label-manual source-type tag-label-secondary-outline tag-label-small content-body-xsmall content-capitalize">
                            {{ collectionSourceTypeEnum.MANUAL | lowercase }}
                          </span>
                          <span *ngSwitchCase="collectionSourceTypeEnum.UNKNOWN" class="tag-label-unknown source-type tag-label-secondary-outline tag-label-small content-body-xsmall content-capitalize">
                            {{ collectionSourceTypeEnum.UNKNOWN | lowercase }}
                          </span>
                          <span *ngSwitchDefault class="tag-label-{{getSourceType(s)}} source-type tag-label-secondary-outline tag-label-small content-body-xsmall content-capitalize"
                                (click)="loadSource($event, s)" appSelectableTooltip [selectableTooltipPopover]="pColSources"
                                [ngClass]="{'cursor-default': !canLoadSource(s)}">
                            <div [ngbPopover]="pColSourcesTemp" #pColSources="ngbPopover" container="body" triggers="manual"
                              [autoClose]="'outside'" popoverClass="white-popover collection-sources-popover">
                              {{getSourceType(s)}}
                            </div>
                            <ng-template #pColSourcesTemp>
                              <div class="popover-container collection-popover-body">
                                <div class="popover-title" [ngClass]="{'button-main-link content-color-active': canLoadSource(s)}">
                                  <span (click)="loadSource($event, s)" [class.cursor-pointer]="canLoadSource(s)">
                                    {{getSourceTitle(s)}}
                                  </span>
                                  <span class="m-x-spacing-xx-s cursor-pointer" (click)="loadSource($event, s, true)" *ngIf="canLoadSource(s)">
                                    <i class="fa-regular fa-up-right-from-square"></i>
                                  </span>
                                </div>
                                <div class="popover-caption">{{s.documents_count}} {{ 'patent' | pluralize: s.documents_count }} added from the following Source. <span *ngIf="!(s.monitor_run_id && !userOwnCollection())">Click on it to reload it.</span></div>
                                <div class="popover-divider m-b-spacing-md"></div>
                                <div class="popover-descriptions" >
                                  <div (click)="loadSource($event, s)"
                                       class="tag-label-{{getSourceType(s)}} source-type tag-label-secondary-outline tag-label-small content-body-xsmall content-capitalize"
                                       [ngClass]="{'cursor-pointer': canLoadSource(s)}">
                                    {{ getSourceType(s) }}
                                  </div>
                                  <div class="source-query-added">Added: {{s.created_at | dateFormat: 'ShortDate'}}</div>
                                  <div class="source-query" [innerHTML]="getSourceQuery(s)"></div>
                                </div>
                              </div>
                            </ng-template>
                          </span>
                        </ng-container>
                        </ng-template>
                      </div>
                      <div class="history-time content-body-xsmall">{{s.created_at | timeReadable}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <hr class="m-0" />

      <app-patent-list-layout [storeService]="collectionStoreService"
                              [documents]="documents"
                              [isLoading]="isLoadingDocuments"
                              [showDashboardActionBar]="true"
                              [alwaysShowDocumentsControlBar]="true">
        <ng-container *ngIf="!documents?.length" alertMessages [ngTemplateOutlet]="noDocumentsMessage"></ng-container>

        <ng-container documentsControlBar [ngTemplateOutlet]="documentsControlBar"></ng-container>

        <ng-container documentsTable [ngTemplateOutlet]="documentsTable"></ng-container>

        <ng-container documentsVisual *ngIf="!isFiltering">
          <app-charts-container [isColumnLayout]="isCombinedMode" [fullWidth]="isAnalysisMode && showTasks && expandSidebar"
                                [storeService]="collectionStoreService"></app-charts-container>
        </ng-container>
      </app-patent-list-layout>

      <app-filters-bar [alwaysBeSticky]="true" (filterRemoved)="onFilterRemoved($event)"
                       [storeService]="collectionStoreService" (clearAll)="clearAllFilters()"></app-filters-bar>
    </div>
  </ng-template>

  <ng-template #noDocumentsMessage>
    <div class="container-fluid my-4">
      <div appNoResults [content]="getAlertMessage()" *ngIf="!isLoadingDocuments && !isLoadingCollection && !haveDocuments"></div>

      <ng-container [ngTemplateOutlet]="documentsFooter"></ng-container>
    </div>
  </ng-template>

  <ng-template #documentsTable>
    <div id="collection" *ngIf="haveDocuments" class="d-flex flex-column align-items-stretch"
         [ngClass]="{'container-fluid': !isCombinedMode && !(showTasks && expandSidebar)}">
      <div class="d-flex justify-content-between align-items-center" [hidden]="isLoadingDocuments">
        <div [hidden]="documents === undefined || documents?.length === 0" class="new-layout-headline">
          <span [hidden]="!totalSelectedPatents" class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{pagination?.total_hits}}
          <span *ngIf="collection.collection_type == 'FAMILY'">
            {{ 'patent family' | pluralize: pagination?.total_hits }}
            ({{pagination?.total_publications}} {{ 'publication' | pluralize: pagination?.total_publications }})
          </span>
          <span *ngIf="collection.collection_type == 'PUBLICATION'">
            {{ 'patent publication' | pluralize: pagination?.total_publications }}
          </span>
          in this list
        </div>
        <div class="tools-bar expand-all" *ngIf="haveDocuments">
          <a href="javascript:void(0)" class="item-bar" (click)="patentTable.openAll()">
            {{ patentTable.openedPatent.length === documents.length ? 'Collapse all' : 'Expand all'}}
            <i class="fas fa-angle-double-down" *ngIf="patentTable.openedPatent.length < documents.length"></i>
            <i class="fas fa-angle-double-up" *ngIf="patentTable.openedPatent.length === documents.length"></i>
          </a>
        </div>
      </div>

      <app-alert type="success" *ngIf="collectionStoreService.getSaveToCollectionSuccess()" [message]="collectionStoreService.getSaveToCollectionSuccess()"></app-alert>

      <div class="psr-patent-table">
        <app-patent-table #patentTable [hidden]="documents.length == 0 || isLoadingDocuments"
                          [patents]="documents" (sort)="onSortPatents($event)" pathUrl="/patent" [linkData]="linkData"
                          [hasLinksToBooleanSearch]="hasLinksToBooleanSearch" [pagination]="pagination"
                          backButtonTitle="Back to list" [currentTask]="currentTask"
                          [showSmartHighlight]="!!sortBySimilarity" [showHighlight]="true" [showRank]="false"
                          [storeService]="collectionStoreService" [sideBarViewMode]="getSideBarViewMode()"
                          [collectionSourceOption]="collectionSourceOption"
                          [collectionSourceUsers]="collectionSourceUsers"
                          (loadSearchSource)="onLoadSearchSource($event)"
                          (loadMonitorSource)="onLoadMonitorSource($event)">
        </app-patent-table>
      </div>

      <ng-container [ngTemplateOutlet]="documentsFooter"></ng-container>
    </div>
  </ng-template>

  <ng-template #documentsControlBar>
    <div class="sticky-top" *ngIf="!isViewByShareCode()  && isListVisible">
      <div class="container-fluid d-flex justify-content-between align-items-center">
        <app-patent-control-bar [columnsToShow]="columnsToShow"
                                [defaultSelectedColumnsToShow]="selectedColumnsCombinedMode"
                                [hasSortBySimilarity]="haveDocuments || !!sortBySimilarity"
                                (sortBySimilarity)="onSortBySimilarity($event)"
                                [hasShowColumnControl]="haveDocuments" [searchService]="collectionService"
                                [hasHarmonizeControl]="haveDocuments && userOwnCollection()"
                                [hasTemporaryLinkControl]="haveDocuments"
                                [hasOctiAIControl]="haveDocuments"
                                [isShared]="isShared"
                                [sharedTooltip]="getSharedTooltip()"
                                [saveSearchHeadline]="saveSearchHeadline"
                                [saveSearchHideAgreementCheck]="haveDocuments"
                                [hasExportControl]="haveDocuments"
                                [hasSaveToCollectionControl]="haveDocuments"
                                [exportAdditionalParams]="{title: collection.name, subtitle: 'Result list'}"
                                [saveSearchTextInput]="collection.name" (sharePatentsEvent)="onSharePatents($event)"
                                (closeShareEvent)="onCloseShare($event)" [saveSearchNameReadOnly]="haveDocuments"
                                [saveSearchResourceId]="collection?.id" [resource]="collection" [permission]="getCollectionPermission()"
                                [hasFilterListControl]="haveDocuments || hasFilters"
                                (filterListEvent)="onAdvancedFilter($event)"
                                [taskShowCreationButton]="true"
                                [taskResourceId]="userOwnCollection() ? collection.id : null"
                                [taskResourceType]="userOwnCollection() ? taskResourceTypeEnum.COLLECTION : null"
                                (taskSaved)="onTaskSaved($event)"
                                [hasAddPatent]="hasReadWritePermission()"
                                [hasMonitor]="haveDocuments && canAnalyzeList() && userService.hasMonitor"
                                [hasLandscape]="haveDocuments && canAnalyzeList() && userService.hasFeature('landscape')"
                                (addPatent)="editPatentsInCollection()" (monitor)="onCollectionConversion('MONITOR')"
                                (landscape)="onCollectionConversion('LANDSCAPE')"
                                [storeService]="collectionStoreService"
                                [patentListScope]="collection?.collection_type">
        </app-patent-control-bar>
      </div>
    </div>
  </ng-template>

  <ng-template #documentsFooter>
    <div class="d-flex align-items-center justify-content-between py-4"
         [class.combined-footer-section]="isCombinedMode" [hidden]="isLoadingDocuments">
      <div class="d-flex align-items-center justify-content-start">
        <a (click)="backToCollections()" class="btn btn-primary-outline btn-md me-3" *ngIf="!isViewByShareCode()">
          <i class="fas fa-long-arrow-alt-left"></i> Back
        </a>
        <a (click)="resetSimilaritySort()" class="btn btn-primary-outline btn-md"
           *ngIf="!!sortBySimilarity"> <i class="fas fa-redo"></i> Clear similarity query
        </a>
      </div>
      <div class="d-flex justify-content-end align-items-center">
        <ng-container *ngIf="isListVisible && haveDocuments">
          <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)"
                         [pageOptions]="[25,50,100]"></app-page-size>
          <app-pagination [pagination]="pagination" (navigatePage)="navigate($event)"></app-pagination>
        </ng-container>
      </div>
    </div>
  </ng-template>

  <ng-template #sidebarTemplate>
    <aside class="sidebar">
      <div class="sidebar-manu d-flex align-items-start flex-column mb-auto sticky-top p-1">
        <a href="javascript:void(0)" class="btn p-2 mt-1" *ngIf="showTasks" [ngClass]="{'active': expandSidebar}"
           (click)="toggleSidebar()" data-toggle="tooltip" data-placement="right" ngbTooltip="Tasks">
          <div class="btn-task"></div>
        </a>
      </div>
    </aside>
  </ng-template>

<ng-template #tagEditTemplate>
  <app-tag-edit [tag]="customTag" [openNewTab]="false">
  </app-tag-edit>
</ng-template>
