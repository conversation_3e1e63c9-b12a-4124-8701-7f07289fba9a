import { NgModule } from '@angular/core';
import { CommonModule, NgOptimizedImage, TitleCasePipe } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HighchartsChartModule } from 'highcharts-angular';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { NgSelectModule } from '@ng-select/ng-select';
import {
  NgbActiveModal,
  NgbDateAdapter,
  NgbDateParserFormatter,
  NgbModule
} from '@ng-bootstrap/ng-bootstrap';
import { TreeModule } from '@ali-hm/angular-tree-component';
import { ColorPickerModule } from 'ngx-color-picker';
import { LightgalleryModule } from 'lightgallery/angular';
import {
  AddToCollectionComponent,
  AlertComponent,
  AutocompleteComponent,
  BaseCardChartComponent,
  BaseTaskOperatorComponent,
  BooleanAdvancedModeComponent,
  BooleanInputComponent,
  BooleanQueryOptionsComponent,
  BooleanSearchLogicComponent,
  RecallTemplateDialogComponent,
  SaveTemplateDialogComponent,
  CitationPatentTableComponent,
  ColSelectorComponent,
  ConfirmationDialogComponent,
  ControlValueAccessorComponent,
  CookieBannerComponent,
  DateInputsComponent,
  DetailAnalyticRadarComponent,
  DoubleMappedPatentNumbersDialogComponent,
  EditablePageComponent,
  ErrorsDialogComponent,
  FiltersBarComponent,
  FilterTermInputComponent,
  FocalTableComponent,
  FooterComponent,
  HarmonizeApplicantsComponent,
  HeaderComponent,
  InlineManualComponent,
  InlineTaskAnswerInputComponent,
  InlineTaskOwnerAnswerViewComponent,
  LegalEventsTableComponent,
  LegalStatusTrackingComponent,
  MonitorDialogComponent,
  PageSizeComponent,
  PageBarComponent,
  PaginationComponent,
  PatentAnnotationDetailComponent,
  PatentControlBarComponent,
  PatentDetailComponent,
  PatentImageComponent,
  PatentListInputComponent,
  PatentTableComponent,
  PdfNotFoundDialogComponent,
  PdfOtherErrorsDialogComponent,
  PdfViewerDialogComponent,
  SearchMenuComponent,
  SearchTitleComponent,
  SemanticInputComponent,
  ShareDialogComponent,
  SortBySimilarityComponent,
  SpinnerComponent,
  SupportComponent,
  TaskAnswerInputComponent,
  TaskAssigneeAnswerComponent,
  TaskFormComponent,
  TaskListComponent,
  TaskOwnerOverviewComponent,
  TaskShortSummaryComponent,
  TaskViewBaseComponent,
  TeamUsersSelectionComponent,
  TextAvatarComponent,
  TooltipComponent,
  TreeSelectionDialogComponent,
  TutorialComponent,
  UploadDialogComponent,
  WeightingBarComponent,
  ZoomChartComponent,
  UserAvatarsComponent,
  PatentLabelsComponent,
  FilterListDialogComponent,
  ContactUsBannerComponent,
  PatentListLayoutComponent,
  TagEditComponent,
  TagsDisplayComponent,
  TagsSelectComponent,
  TagItemComponent,
  FooterBannerComponent,
  OctiPanelComponent,
  BibliographicLinksComponent,
  TaskStatsComponent,
  PermissionSettingComponent
} from '@shared/components';
import {
  BypassSecurityPipe,
  CountSerialPipe,
  CountStatPipe,
  DateFormatPipe,
  BooleanHighlightPipe,
  HumanReadableNumberPipe,
  InterpolateColorsPipe,
  SafeHtmlPipe,
  TagParserPipe,
  TimeReadablePipe,
  TruncatePipe,
  UsersTitleTextPipe,
  UserTitlePipe,
  PluralizePipe
} from '@core/pipes';
import {
  InnerHtmlDirective,
  MaskDateDirective,
  PhotosBrowserDirective,
  ScrollSpyDirective,
  TableSortIconDirective,
  TextareaAutoresizeDirective,
  PriorityPlusMenuDirective,
  DropdownToggleDirective,
  TextEllipsisDirective,
  CombinedLayoutDirective,
  BetaDirective,
  AutofocusDirective,
  SelectableTooltipDirective,
  TruncatableTooltipDirective,
  ClickOutsideDirective,
  MouseHoverDirective,
  ScrollbarDetectorDirective,
  NoResultsDirective,
  ScreenSizeDetectorDirective,
  ParagraphNumberDirective,
} from '@core/directives';
import { FolderFormComponent } from '@collections/folder-form/folder-form.component';
import { CollectionFormComponent } from '@collections/collection-form/collection-form.component';
import { PatentInfoComponent } from './components/patent-info/patent-info.component';
import { MentionModule } from 'angular-mentions';
import {
  ClassificationFilterBrowserComponent
} from './components/classification-filter-browser/classification-filter-browser.component';
import { NotificationsComponent } from './components/notifications/notifications.component';
import {
  CockpitWidgetComponent,
  MonitorRunsWidgetComponent,
  SearchHistoryWidgetComponent
} from '@shared/widgets';
import { ExportDialogComponent } from './components/export-dialog/export-dialog.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { QRCodeModule } from 'angularx-qrcode';
import { NgbDateLocaleAdapter, NgbDateLocaleParserFormatter } from './components/date-picker/NgbDatePickerCustom';
import { DashboardActionBarComponent } from './components/dashboard-action-bar/dashboard-action-bar.component';
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';
import { WorkspaceMenuComponent } from './components/workspace-menu/workspace-menu.component';
import { TagSelectorComponent } from './components/tag-selector/tag-selector.component';
import { TagLabelComponent } from './components/tag-label/tag-label.component';
import { TranslateLanguageDialogComponent } from './components/translate-language-dialog/translate-language-dialog.component';
import { BoostSearchComponent } from './components/boost-search/boost-search.component';
import { SemanticFiltersDialogComponent } from './components/semantic-filters-dialog/semantic-filters-dialog.component';
import { ToastsComponent } from './components/toasts/toasts.component';
import { PopperComponent } from './components/popper/popper.component';
import { ModalDialogComponent } from './components/modal-dialog/modal-dialog.component';
import { UserAvatarComponent } from './components/user-avatar/user-avatar.component';
import { UsersSelectComponent } from './components/users-select/users-select.component';
import { UsersDisplayComponent } from './components/users-display/users-display.component';
import { SmartHighlightIconComponent } from './components/smart-highlight-icon/smart-highlight-icon.component';
import { CorporateEntitiesBrowserComponent } from './components/corporate-entities-browser/corporate-entities-browser.component';
import { TechnologyFieldBrowserComponent } from './components/technology-field-browser/technology-field-browser.component';
import { NgxMaskDirective, NgxMaskPipe, provideNgxMask } from 'ngx-mask';
import { TopicsSelectorComponent } from './components/topics-selector/topics-selector.component';
import { TeamsSelectorComponent } from './components/teams-selector/teams-selector.component';
import { UserDialogueComponent } from './components/user-dialogue/user-dialogue.component';
import { LoadingDropdownComponent } from './components/loading-dropdown/loading-dropdown.component';
import { AuthorityLegalStatusComponent } from './components/authority-legal-status/authority-legal-status.component';
import { NplFiltersDialogComponent } from './components/npl-filters-dialog/npl-filters-dialog.component';
import { RatingFormComponent } from '@patent/patent-ratings/rating-form/rating-form.component';
import { SideSectionHeaderComponent } from '@patent/side-section-header/side-section-header.component';
import { RequestBaseViewComponent } from '@patent/patent-ratings/request-base-view/request-base-view.component';
import { RequestResultCardComponent } from '@patent/patent-ratings/request-result-card/request-result-card.component';
import { RequestAnswerComponent } from '@patent/patent-ratings/request-answer/request-answer.component';
import { RequestCardComponent } from '@patent/patent-ratings/request-card/request-card.component';
import { IntermediateStepComponent } from '@patent/patent-ratings/intermediate-step/intermediate-step.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbModule,
    ReactiveFormsModule,
    HighchartsChartModule,
    NgxSliderModule,
    NgSelectModule,
    NgxPaginationModule,
    ColorPickerModule,
    TreeModule,
    DragDropModule,
    MentionModule,
    QRCodeModule,
    NgxExtendedPdfViewerModule,
    NgOptimizedImage,
    LightgalleryModule,
    NgxMaskDirective,
    NgxMaskPipe
  ],
  declarations: [
    HeaderComponent,
    FooterComponent,
    SpinnerComponent,
    DateInputsComponent,
    AutocompleteComponent,
    BaseCardChartComponent,
    FiltersBarComponent,
    PatentDetailComponent,
    PatentImageComponent,
    PaginationComponent,
    ZoomChartComponent,
    ColSelectorComponent,
    DetailAnalyticRadarComponent,
    SemanticInputComponent,
    ErrorsDialogComponent,
    InlineManualComponent,
    TooltipComponent,
    SafeHtmlPipe,
    TruncatePipe,
    CountStatPipe,
    CountSerialPipe,
    TimeReadablePipe,
    UserTitlePipe,
    UsersTitleTextPipe,
    BypassSecurityPipe,
    TagParserPipe,
    DateFormatPipe,
    BooleanHighlightPipe,
    HumanReadableNumberPipe,
    InterpolateColorsPipe,
    PluralizePipe,
    ConfirmationDialogComponent,
    PatentControlBarComponent,
    FocalTableComponent,
    PatentTableComponent,
    TreeSelectionDialogComponent,
    PageSizeComponent,
    ShareDialogComponent,
    MaskDateDirective,
    BooleanInputComponent,
    PdfNotFoundDialogComponent,
    PdfViewerDialogComponent,
    CitationPatentTableComponent,
    PdfViewerDialogComponent,
    CookieBannerComponent,
    FolderFormComponent,
    CollectionFormComponent,
    AddToCollectionComponent,
    InnerHtmlDirective,
    EditablePageComponent,
    HarmonizeApplicantsComponent,
    PageBarComponent,
    AlertComponent,
    ScrollSpyDirective,
    TextareaAutoresizeDirective,
    PriorityPlusMenuDirective,
    DropdownToggleDirective,
    TextEllipsisDirective,
    CombinedLayoutDirective,
    BetaDirective,
    AutofocusDirective,
    NoResultsDirective,
    SearchTitleComponent,
    WeightingBarComponent,
    PdfOtherErrorsDialogComponent,
    BooleanAdvancedModeComponent,
    TextAvatarComponent,
    PdfOtherErrorsDialogComponent,
    LegalEventsTableComponent,
    PhotosBrowserDirective,
    TableSortIconDirective,
    MonitorDialogComponent,
    PatentInfoComponent,
    UploadDialogComponent,
    ClassificationFilterBrowserComponent,
    NotificationsComponent,
    SearchHistoryWidgetComponent,
    MonitorRunsWidgetComponent,
    CockpitWidgetComponent,
    ExportDialogComponent,
    PatentListInputComponent,
    TutorialComponent,
    PatentAnnotationDetailComponent,
    SupportComponent,
    ControlValueAccessorComponent,
    TeamUsersSelectionComponent,
    TaskFormComponent,
    TaskViewBaseComponent,
    TaskOwnerOverviewComponent,
    TaskListComponent,
    TaskShortSummaryComponent,
    TaskAssigneeAnswerComponent,
    DoubleMappedPatentNumbersDialogComponent,
    SortBySimilarityComponent,
    SearchMenuComponent,
    FilterTermInputComponent,
    LegalStatusTrackingComponent,
    BaseTaskOperatorComponent,
    TaskAnswerInputComponent,
    InlineTaskAnswerInputComponent,
    InlineTaskOwnerAnswerViewComponent,
    BooleanQueryOptionsComponent,
    RecallTemplateDialogComponent,
    SaveTemplateDialogComponent,
    BooleanSearchLogicComponent,
    UserAvatarsComponent,
    PatentLabelsComponent,
    FilterListDialogComponent,
    DashboardActionBarComponent,
    TagSelectorComponent,
    TagLabelComponent,
    ContactUsBannerComponent,
    WorkspaceMenuComponent,
    PatentListLayoutComponent,
    TranslateLanguageDialogComponent,
    BoostSearchComponent,
    SemanticFiltersDialogComponent,
    TagEditComponent,
    TagsDisplayComponent,
    TagsSelectComponent,
    TagItemComponent,
    ToastsComponent,
    PopperComponent,
    FooterBannerComponent,
    ModalDialogComponent,
    UserAvatarComponent,
    UsersSelectComponent,
    UsersDisplayComponent,
    SmartHighlightIconComponent,
    CorporateEntitiesBrowserComponent,
    SelectableTooltipDirective,
    BibliographicLinksComponent,
    TechnologyFieldBrowserComponent,
    ScreenSizeDetectorDirective,
    BibliographicLinksComponent,
    TruncatableTooltipDirective,
    AuthorityLegalStatusComponent,
    TopicsSelectorComponent,
    TeamsSelectorComponent,
    UserDialogueComponent,
    ClickOutsideDirective,
    LoadingDropdownComponent,
    MouseHoverDirective,
    ScrollbarDetectorDirective,
    ParagraphNumberDirective,
    NplFiltersDialogComponent,
    RatingFormComponent,
    OctiPanelComponent,
    SideSectionHeaderComponent,
    RequestBaseViewComponent,
    RequestResultCardComponent,
    RequestAnswerComponent,
    RequestCardComponent,
    TaskStatsComponent,
    IntermediateStepComponent,
    PermissionSettingComponent
  ],
  exports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgbModule,
    NgSelectModule,
    TreeModule,
    QRCodeModule,
    HeaderComponent,
    FooterComponent,
    ErrorsDialogComponent,
    SpinnerComponent,
    DateInputsComponent,
    AutocompleteComponent,
    BaseCardChartComponent,
    FiltersBarComponent,
    PatentDetailComponent,
    PaginationComponent,
    PatentImageComponent,
    ZoomChartComponent,
    ColSelectorComponent,
    DetailAnalyticRadarComponent,
    SemanticInputComponent,
    InlineManualComponent,
    TooltipComponent,
    PatentControlBarComponent,
    FocalTableComponent,
    PatentTableComponent,
    PageSizeComponent,
    ShareDialogComponent,
    SafeHtmlPipe,
    TruncatePipe,
    CountStatPipe,
    CountSerialPipe,
    TimeReadablePipe,
    MaskDateDirective,
    CombinedLayoutDirective,
    BetaDirective,
    AutofocusDirective,
    TextareaAutoresizeDirective,
    HumanReadableNumberPipe,
    InterpolateColorsPipe,
    PluralizePipe,
    BooleanInputComponent,
    PdfNotFoundDialogComponent,
    PdfViewerDialogComponent,
    CitationPatentTableComponent,
    CookieBannerComponent,
    FolderFormComponent,
    CollectionFormComponent,
    AddToCollectionComponent,
    EditablePageComponent,
    AddToCollectionComponent,
    HarmonizeApplicantsComponent,
    PageBarComponent,
    AlertComponent,
    SearchTitleComponent,
    WeightingBarComponent,
    BooleanAdvancedModeComponent,
    TextAvatarComponent,
    WeightingBarComponent,
    UserTitlePipe,
    UsersTitleTextPipe,
    BypassSecurityPipe,
    TagParserPipe,
    DateFormatPipe,
    BooleanHighlightPipe,
    PhotosBrowserDirective,
    LegalEventsTableComponent,
    TableSortIconDirective,
    PatentInfoComponent,
    ClassificationFilterBrowserComponent,
    NotificationsComponent,
    SearchHistoryWidgetComponent,
    MonitorRunsWidgetComponent,
    CockpitWidgetComponent,
    ExportDialogComponent,
    PatentListInputComponent,
    TutorialComponent,
    PatentAnnotationDetailComponent,
    SupportComponent,
    ControlValueAccessorComponent,
    TeamUsersSelectionComponent,
    TaskFormComponent,
    TaskViewBaseComponent,
    TaskOwnerOverviewComponent,
    TaskListComponent,
    TaskShortSummaryComponent,
    TaskAssigneeAnswerComponent,
    DoubleMappedPatentNumbersDialogComponent,
    SortBySimilarityComponent,
    SearchMenuComponent,
    FilterTermInputComponent,
    BaseTaskOperatorComponent,
    TaskAnswerInputComponent,
    InlineTaskAnswerInputComponent,
    InlineTaskOwnerAnswerViewComponent,
    BooleanQueryOptionsComponent,
    RecallTemplateDialogComponent,
    SaveTemplateDialogComponent,
    BooleanSearchLogicComponent,
    UserAvatarsComponent,
    PatentLabelsComponent,
    FilterListDialogComponent,
    DashboardActionBarComponent,
    NgxExtendedPdfViewerModule,
    ContactUsBannerComponent,
    WorkspaceMenuComponent,
    TagSelectorComponent,
    TagLabelComponent,
    PatentListLayoutComponent,
    TranslateLanguageDialogComponent,
    BoostSearchComponent,
    SemanticFiltersDialogComponent,
    InnerHtmlDirective,
    PriorityPlusMenuDirective,
    DropdownToggleDirective,
    TextEllipsisDirective,
    NoResultsDirective,
    TagEditComponent,
    TagsDisplayComponent,
    TagsSelectComponent,
    TagItemComponent,
    ToastsComponent,
    PopperComponent,
    FooterBannerComponent,
    ModalDialogComponent,
    UserAvatarComponent,
    UsersSelectComponent,
    UsersDisplayComponent,
    SmartHighlightIconComponent,
    CorporateEntitiesBrowserComponent,
    TechnologyFieldBrowserComponent,
    SelectableTooltipDirective,
    ScreenSizeDetectorDirective,
    TopicsSelectorComponent,
    TeamsSelectorComponent,
    UserDialogueComponent,
    ClickOutsideDirective,
    LoadingDropdownComponent,
    TruncatableTooltipDirective,
    MouseHoverDirective,
    ScrollbarDetectorDirective,
    AuthorityLegalStatusComponent,
    ParagraphNumberDirective,
    NplFiltersDialogComponent,
    RatingFormComponent,
    OctiPanelComponent,
    SideSectionHeaderComponent,
    RequestBaseViewComponent,
    RequestResultCardComponent,
    RequestAnswerComponent,
    RequestCardComponent,
    TaskStatsComponent,
    IntermediateStepComponent,
    PermissionSettingComponent
  ],
  providers: [
    NgbActiveModal,
    TruncatePipe,
    TimeReadablePipe,
    UsersTitleTextPipe,
    UserTitlePipe,
    BypassSecurityPipe,
    SafeHtmlPipe,
    TagParserPipe,
    DateFormatPipe,
    BooleanHighlightPipe,
    HumanReadableNumberPipe,
    InterpolateColorsPipe,
    TitleCasePipe,
    PluralizePipe,
    CountSerialPipe,
    provideNgxMask(),
    {provide: NgbDateAdapter, useClass: NgbDateLocaleAdapter},
    {provide: NgbDateParserFormatter, useClass: NgbDateLocaleParserFormatter}
  ]
})
export class SharedModule {
}
