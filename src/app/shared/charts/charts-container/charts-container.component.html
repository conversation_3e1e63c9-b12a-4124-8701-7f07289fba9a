<div class="chart-section tabs-container" id="{{storeService.chartDashboardType}}-chart-section"
  data-intercom-target="chart-section">
  <div class="tools-bar chart-bar" *ngIf="!isColumnLayout">
    <div class="d-flex chart-areas p-0" [ngClass]="{'container-fluid': !fullWidth, 'pe-0': isCombinedMode}">
      <ng-container *ngIf="true; then tabNames"></ng-container>
    </div>
  </div>
  <div class="chart-container" [ngClass]="{'container-fluid': !fullWidth, 'pe-0': isCombinedMode}">
    <div class="d-flex justify-content-between mb-0 column-layout" *ngIf="isColumnLayout">
      <div class="tabs-title section-title-text align-self-center">
        {{isCustomChartCategory ? 'My dashboard' : 'Visual analysis'}}
      </div>
      <div class="tools-bar-as-dropdown align-self-center">
        <div class="" ngbDropdown>
          <div class="caret-off text-capitalize cursor-pointer" ngbDropdownToggle style="min-width: 212px;">
            <span class="form-control" *ngIf="!isCustomChartCategory">
              <img alt="" [src]="'assets/images/layout2022/icon-' + activeChartCategoryIcon + '-hover.svg'"
                style="width: 15px;" /> {{ activeChartCategoryTitle }}
            </span>

            <ng-container *ngFor="let ds of customChartCategories;">
              <span class="form-control" *ngIf="isActiveChartCategory(storeService.generateCustomChartCategoryId(ds))">
                {{ds.name}}</span>
            </ng-container>
            <div class="dropdown-icon"></div>
          </div>
          <div ngbDropdownMenu>
            <ng-container *ngIf="true; then tabNames"></ng-container>
          </div>
        </div>
      </div>
    </div>

    <app-alert type="warning" *ngIf="tabsSaved" message="Current chart selection has been saved to your profile.">
    </app-alert>
    <app-alert type="info" *ngIf="chartDisclaimer" [message]="chartDisclaimer" [classes]="!storeService.isCombinedMode? 'mt-3': ''"></app-alert>

    <div class="charts-wrap charts-container" [class.none-max-height]="!isCombinedMode" [class.p-t-spacing-md]="!isCombinedMode"
      data-intercom-target="charts-container">
      <div class="d-flex flex-wrap mb-4 row" *ngIf="activeDefaultCategoryCharts?.length">
        <ng-container *ngFor="let chart of activeDefaultCategoryCharts">
          <div class="mb-4" *ngIf="!chart.hide"
            [ngClass]="isColumnLayout || storeService.singleChartColumn || chart.size === 'lg' ? 'col-md-12' : 'col-md-6'">
            <app-chart-factory [chart]="chart" [storeService]="storeService"></app-chart-factory>
          </div>
        </ng-container>
      </div>

      <div *ngFor="let ds of customChartCategories;">
        <app-chart-dashboard *ngIf="isActiveChartCategory(storeService.generateCustomChartCategoryId(ds))"
          [isColumnLayout]="isColumnLayout" [customChartCategory]="ds"
          [(arrangeDashBoard)]="storeService.arrangeDashboard" [storeService]="storeService">
        </app-chart-dashboard>
      </div>
      <ng-container *ngIf="!activeDefaultCategoryCharts?.length && !customChartCategories?.length">
        <br>
        <app-alert type="info" message="This category has no charts"></app-alert>
      </ng-container>
    </div>
  </div>
  <app-zoom-chart [showFavoriteOption]="false" [storeService]="storeService"></app-zoom-chart>
</div>
<ng-template #tabNames>
  <div [ngClass]="{'d-flex justify-content-between w-100' : !isColumnLayout }">
    <div [ngClass]="{ 'tab-column d-flex flex-wrap': !isColumnLayout }">
      <div class="tabs" *ngFor="let category of defaultChartCategories">
        <a href="javascript:void(0)" *ngIf="(!category.newCategory || userService.isAdmin()) && storeService.hasFeature(category)"
          (click)="toggle(category.category)" [class]="'chart-icon chart-icon-' + category.category + ' item-bar'"
          [ngClass]="{ active: isActiveChartCategory(category.category), 'py-1 dropdown-item': isColumnLayout, 'tab-dashboard': !isColumnLayout && category.newCategory}">
          <span>{{ category.title }}</span>
        </a>
      </div>
      <div class="tabs" *ngFor="let ds of customChartCategories;">
        <a href="javascript:void(0)"
          (click)="toggle(storeService.generateCustomChartCategoryId(ds))" class="item-bar"
          [ngClass]="{ active: isActiveChartCategory(storeService.generateCustomChartCategoryId(ds)), 'ps-3 dropdown-item': isColumnLayout, 'tab-dashboard': !isColumnLayout }">
          <span>{{ ds.name }}</span>
        </a>
      </div>
      <div class="tabs d-flex align-items-center p-l-spacing-sm">
        <a href="javascript:void(0)" ngbTooltip="Add new dashboard" (click)="addCustomChartCategory()"
          class="item-bar chart-icon border-0 p-spacing-none" *ngIf="!isColumnLayout"
          data-intercom-target="add-dashboard">
          <i class="fa-solid fa-plus fa-fw"></i>
        </a>
      </div>
    </div>
    <div class="tab-column d-flex align-items-start" *ngIf="!isColumnLayout">
      <a href="javascript:void(0)" ngbTooltip="Grid layout" class="item-bar chart-icon chart-icon-grid-2columns border-0 pe-0"
        [ngClass]="{ active: !storeService.singleChartColumn }" (click)="setSingleChartColumn(false)">
      </a>
      <a href="javascript:void(0)" ngbTooltip="Single column layout"
        class="item-bar chart-icon chart-icon-grid-1column border-0 pe-0" [ngClass]="{ active: storeService.singleChartColumn }"
        (click)="setSingleChartColumn(true)">
      </a>
    </div>
  </div>
</ng-template>
