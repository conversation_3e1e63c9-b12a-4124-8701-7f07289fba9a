import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import {
  BooleanSearchService,
  CitationSearchService,
  Collection,
  CollectionService,
  Folder,
  LandscapeService,
  MonitorService,
  PaginationMetadata, PatentListScopeEnum,
  SemanticSearchService,
  UserService
} from '@core/services';
import { CollaborationPermissionEnum } from '@core/models';
import { debounceTime, finalize, take } from 'rxjs/operators';
import { BehaviorSubject, Subscription } from 'rxjs';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BaseStoreService, CollectionStoreService, MonitorStoreService } from '@core/store';
import { FolderFormComponent } from '@collections/folder-form/folder-form.component';

@Component({
  selector: 'app-add-to-collection',
  templateUrl: './add-to-collection.component.html',
  styleUrls: ['./add-to-collection.component.scss']
})
export class AddToCollectionComponent implements OnInit, OnDestroy {
  @Input()
  searchService: SemanticSearchService | CitationSearchService | BooleanSearchService | CollectionService |
    MonitorService | LandscapeService;
  @Input() getCollectionPublicationNumbers: boolean = false;
  @Input() canCreateList: boolean = true;
  @Input() canCreateFolder: boolean = true;
  @Input() showHeader: boolean = true;
  @Input() customTitle: string;
  @Input() storeService: BaseStoreService;
  @Input() permission?: CollaborationPermissionEnum = CollaborationPermissionEnum.READ_WRITE;
  @Output() listSelection = new EventEmitter<Collection>(null);

  filter: string = null;
  collections: Collection[] = [];
  sharedCollections: Collection[] = null;
  folders: Folder[] = [];
  sharedFolders: Folder[] = null;
  isLoadingCollections = false;
  isLoadingSharedCollections = false;
  isLoadingFolders = false;
  isLoadingSharedFolders: boolean;
  currentSavingCollectionId: number = null;
  openFolders: {[folderId: number]: {collections: Collection[], folders: Folder[], pagination: PaginationMetadata}} = {};

  openSharedCollections = false;
  loadingFolders = {};
  currentFolder: Folder;
  saveSearchErrors: string[];
  saveSearchSuccessMsg: string = null;
  title = '';
  collectionName = '';
  isSaving = false;
  selectedCollection: Collection;
  @Input() patentListScope: PatentListScopeEnum = PatentListScopeEnum.FAMILY;

  private filterSubject = new BehaviorSubject<boolean>(false);
  private pagination: PaginationMetadata;
  private nextPageCollectionsSubject = new BehaviorSubject<boolean>(false);
  private sharedPagination: PaginationMetadata;
  private nextPageSharedCollectionsSubject = new BehaviorSubject<boolean>(false);
  private subscriptions = new Subscription();

  constructor(
    private collectionService: CollectionService,
    public collectionsStoreService: CollectionStoreService,
    private monitorStoreService: MonitorStoreService,
    private modalService: NgbModal,
    public userService: UserService,
    public activeModal: NgbActiveModal
  ) {
  }

  get isFamilyCollectionType() {
    return this.patentListScope === PatentListScopeEnum.FAMILY;
  }

  get canLoadMore(): boolean {
    return this.pagination && this.pagination.current_page < this.pagination.last_page;
  }

  get canLoadMoreShared(): boolean{
    return this.sharedPagination && this.sharedPagination.current_page < this.sharedPagination.last_page;
  }

  ngOnInit() {
    const selectedPublications$ = this.storeService.selectedPublications$.subscribe({
      next: val => {
        const postfix = val && val.length === 1 ? '' : 's';
        this.title = `Select where you want to save your patent${postfix}`;
      }
    });
    this.subscriptions.add(selectedPublications$);

    const filterSubject$ = this.filterSubject
      .pipe(debounceTime(300))
      .subscribe({
        next: val => {
          if (val) {
            this.filterFolders();
            this.filterCollections();
            this.filterSharedFolders();
            this.filterSharedCollections();
          }
        }
      });
    this.subscriptions.add(filterSubject$);

    const nextPageCollectionsSubject$ = this.nextPageCollectionsSubject
      .pipe(debounceTime(300))
      .subscribe({
        next: val => {
          if (val && !this.isLoadingCollections) {
            this.pagination.current_page += 1;
            this.loadCollections(null);
          }
        }
      });
    this.subscriptions.add(nextPageCollectionsSubject$);

    this.subscriptions.add(this.nextPageSharedCollectionsSubject
      .pipe(debounceTime(300))
      .subscribe({
        next: val => {
          if (val && !this.isLoadingSharedCollections) {
            this.sharedPagination.current_page += 1;
            this.loadSharedCollections(null);
          }
        }
      }));

    this.loadData();
  }
  private loadData(){
    this.loadFolders(null);
    this.loadCollections(null);
    this.loadSharedFolders(null);
    this.loadSharedCollections(null);
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  onFilterChanged() {
    this.filterSubject.next(true);
  }

  onNewFolderClicked() {
    const modal = this.modalService.open(FolderFormComponent, {size: 'lg'});
    modal.result.then(value => {
      this.refreshFolders();
    }, reason => {
      this.refreshFolders();
    });
  }

  onFolderClicked(f: Folder, isShared?: boolean) {
    if (this.openFolders[f.id]) {
      delete this.openFolders[f.id];
      this.currentFolder = null;
    } else {
      this.currentFolder = f;
      if(!this.openFolders[f.id]){
        this.openFolders[f.id] = {collections :[], folders :[], pagination :null}
      }
      if(isShared){
        this.loadSharedFolders(f.id);
        this.loadSharedCollections(f.id);
      } else {
        this.loadFolders(f.id);
        this.loadCollections(f.id);
      }
    }
  }

  onCollectionsScrolled(event: Event, collectionsEle: HTMLDivElement) {
    const endOfCollections = collectionsEle.scrollTop + collectionsEle.offsetHeight >= collectionsEle.scrollHeight - 100;
    if (endOfCollections && this.canLoadMore) {
      this.nextPageCollectionsSubject.next(true);
    }
  }

  onSharedCollectionsScrolled(event: Event, collectionsEle: HTMLDivElement) {
    const endOfCollections = collectionsEle.scrollTop + collectionsEle.offsetHeight >= collectionsEle.scrollHeight - 100;
    if (endOfCollections && this.canLoadMoreShared) {
      this.nextPageSharedCollectionsSubject.next(true);
    }
  }

  isFolderActive(f: Folder): boolean {
    return this.currentFolder && this.currentFolder.id === f.id;
  }

  buildUpdateCollectionPayload(){
    const payload = {
      add: {}
    };

    if (this.isMonitorService()) {
      payload.add['monitor_run_id'] = this.monitorStoreService.selectedMonitorRun;
    } else {
      payload.add['search_hash'] = this.storeService.searchHash;
    }

    const freeTextQuery = this.storeService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload.add['free_text_query'] = freeTextQuery;
    }

    if(this.storeService.isPublications){
      const publicationNumbers = this.getStoredPublicationIds();
      if (publicationNumbers && publicationNumbers.length > 0) {
        payload.add['publication_numbers'] = publicationNumbers;
      }
    } else {
      const documentIds = this.getStoredDocumentIds();
      if (documentIds && documentIds.length > 0) {
        payload.add['document_ids'] = documentIds;
      }
    }
    return payload;
  }

  saveSearchToCollection(c: Collection, folder: Folder) {
    if(this.collectionsStoreService?.collection && c.id === this.collectionsStoreService?.collection?.id){
      return;
    }
    this.selectedCollection = c;
    if (this.getCollectionPublicationNumbers) {
      this.listSelection.emit(c);
    } else if (c && c.id && !this.currentSavingCollectionId) {
      if(this.patentListScope !== c.collection_type){
        this.saveSearchErrors = [`Your selection of ${this.patentListScope.toLowerCase()} based documents can't be saved in  ${c.collection_type.toLowerCase()} based document collection`]
        return ;
      }
      this.currentFolder = folder;

      const payload = this.buildUpdateCollectionPayload();

      this.currentSavingCollectionId = c.id;
      this.saveSearchSuccessMsg = null;
      this.saveSearchErrors = null;

      const updateDocuments$ = this.collectionService.updateDocuments(c.id, payload)
        .pipe(take(1), finalize(() => this.currentSavingCollectionId = null))
        .subscribe({next: data => {
          this.refreshCollections();
          this.saveSearchSuccessMsg = `Your selection has been saved to the list
          <a href="/collections/${c.id}" target="_blank">*${c.name}*</a>`;
          this.collectionsStoreService.setSaveToCollectionSuccess(this.saveSearchSuccessMsg);
          this.activeModal.close(this.saveSearchSuccessMsg);
        },error: ({error}) => {
          console.error(error);
          this.saveSearchErrors = error.status === 400 ? error.message : this.getErrorMessage(c.name);
        }});
      this.subscriptions.add(updateDocuments$);
    }
  }

  onCloseClicked() {
    this.close();
  }

  saveCollection() {
    if (!this.collectionName || this.isSaving) {
      return;
    }

    this.isSaving = true;
    const payload = this.buildCollectionPayload();

    const createCollection$ = this.collectionService.createCollection(payload).pipe(take(1), finalize(() => {
      this.isSaving = false;
      this.collectionName = '';
    }))
      .subscribe({next: (c) => {
        this.collectionsStoreService.setRefreshCollections(true);
        this.saveSearchSuccessMsg = `Your selection has been saved to the list "<a href="/collections/${c.id}" target="_blank">${c.name}</a>"`;
        this.collectionsStoreService.setSaveToCollectionSuccess(this.saveSearchSuccessMsg);
        this.activeModal.close(this.saveSearchSuccessMsg);
        // this.refreshCollections();
      }, error: ({error}) => {
        console.error(error);
        this.saveSearchErrors = error.details ? Object.keys(error.details).map(key => key + ': ' + error.details[key]) : [error.message];
      }});
    this.subscriptions.add(createCollection$);
  }

  userOwnCollection(c: Collection): boolean {
    return c.user_id === this.userService.getUser()?.profile?.id;
  }

  private getErrorMessage(collectionName = null): [string] {
    const collectionTitle = collectionName ? `list *${collectionName}*.` : 'new list';
    const documentIds = this.getStoredDocumentIds();
    const title = !documentIds || documentIds.length === 0 ? 'all search results' :
      (documentIds.length + ' selected ' + (documentIds.length > 1 ? 'patents' : 'patent'));
    return [`Error when saving ${title} to ${collectionTitle}.`];
  }

  private refreshCollections() {
    const folderId = this.currentFolder ? this.currentFolder.id : null;

    if (!folderId) {
      this.collections = [];
      this.pagination.current_page = 1;
    }

    this.loadCollections(folderId);
  }

  noListMessage(): string{
    if(!this.canCreateList){
      return `You haven't created any lists yet. Please create a list first.`;
    }
    return 'You do not have any lists. You can create a list first.';
  }

  private refreshFolders() {
    this.folders = [];
    this.loadFolders(null);
  }

  private close() {
    this.currentFolder = null;
  }

  private getStoredDocumentIds(): string[] {
    const documentIds = this.storeService.selectedPatentIds.map(o => o.toString());

    return documentIds ? documentIds : [];
  }

  private getStoredPublicationIds(): string[]{
    const ids = this.storeService.selectedPublications;
    return ids ? ids : [];
  }

  private loadFolders(folderId: number) {
    //if ((folderId && this.loadingFolders[folderId]) || this.isLoadingFolders) { return; }

    const payload = this.buildFolderPayload(folderId);;

    if (this.isFiltering()) {
      payload['name'] = `like:%${this.filter.trim()}%`;
    }

    if (folderId) {
      this.loadingFolders[folderId] = true;
    } else {
      this.folders = [];
      this.isLoadingFolders = true;
    }

    const getFolders$ = this.collectionService.getFolders(payload)
      .pipe(take(1), finalize(() => {
        if (folderId) {
          this.loadingFolders[folderId] = false;
        } else {
          this.isLoadingFolders = false;
        }
      }))
      .subscribe({next: ({folders, page}) => {
        if (folderId) {
          this.openFolders[folderId].folders = folders;
        } else {
          this.folders = folders;
        }
      }, error: error => {
        console.error(error);
      }});
    this.subscriptions.add(getFolders$);
  }

  private buildCollectionsPayload(folderId: number, pagination: PaginationMetadata) {
    const page = pagination ? pagination.current_page : 1;

    const payload = {
      page
    };

    const _isFiltering = this.isFiltering();

    if (folderId) {
      payload['folder_id'] = folderId;
    } else {
      if (!_isFiltering) {
        payload['folder_id'] = 'is:null';
      }
    }

    if (_isFiltering) {
      payload['name'] = `like:%${this.filter.trim()}%`;
    }
    if(this.patentListScope){
      payload['collection_type'] = this.patentListScope
    }
    payload['exclude_monitor_runs'] = 1;
    payload['exclude_tagged_documents'] = 1;

    return payload;
  }

  private isFiltering(): boolean {
    return this.filter && this.filter.trim().length > 0;
  }
  private buildFolderPayload(folderId: number){
    const payload = {};
    const _isFiltering = this.isFiltering();

    if (folderId) {
      payload['folder_id'] = folderId;
    } else {
      if (!_isFiltering) {
        payload['folder_id'] = 'is:null';
      }
    }

    if (_isFiltering) {
      payload['name'] = `like:%${this.filter.trim()}%`;
    }
    return payload
  }

  private loadSharedFolders(folderId: number){
    //if ((folderId && this.loadingFolders[folderId]) || this.isLoadingSharedFolders) { return; }
    const payload = this.buildFolderPayload(folderId);

    if (folderId) {
      this.loadingFolders[folderId] = true;
    } else {
      this.isLoadingSharedFolders = true;
    }
    this.subscriptions.add(this.collectionService.getShareFolders(payload)
    .pipe(finalize(() => {
      if (folderId) {
        this.loadingFolders[folderId] = false;
      } else {
        this.isLoadingSharedFolders = false;
      }
    })).subscribe({
      next: (data) => {
        const sharedFolders = this.permission? data.folders.filter(f=> !f.collaboration || f.collaboration.permission === this.permission) : data.folders;
        if (folderId) {
          this.openFolders[folderId].folders = sharedFolders;
        } else {
          this.sharedFolders = sharedFolders;
        }
      }, error: (error) => console.error(error)
    }));
  }

  private loadSharedCollections(folderId: number, pagination?: PaginationMetadata){
    //if ((folderId && this.loadingFolders[folderId]) || this.isLoadingSharedCollections) { return; }
    if(!pagination){
      pagination = this.sharedPagination;
    }

    if (folderId) {
      this.loadingFolders[folderId] = true;
    } else {
      this.isLoadingSharedCollections = true;
    }

    const payload = this.buildCollectionsPayload(folderId, pagination);
    payload['creation_mode'] = 'DEFAULT';
    this.subscriptions.add(this.collectionService.getSharedCollections(payload)
    .pipe(finalize(() => {
      if (folderId) {
        this.loadingFolders[folderId] = false;
      } else {
        this.isLoadingSharedCollections = false;
      }
    })).subscribe({
      next: (data) => {
        const incomingCollection = data.result_collections.filter(c=> !this.permission || c.collaboration.permission === this.permission);
        if (folderId) {
          this.openFolders[folderId].collections = [...this.openFolders[folderId].collections, ...incomingCollection];
          this.openFolders[folderId].pagination = data.page;
        } else {
          this.sharedCollections = [
            ...(this.sharedCollections ?? []),
            ...incomingCollection
          ];
          this.sharedPagination = data.page;
        }
      },
      error: (error) => console.error(error)
    }));
  }

  private loadCollections(folderId: number, pagination?: PaginationMetadata) {
    //if ((folderId && this.loadingFolders[folderId]) || this.isLoadingCollections) { return; }

    if(!pagination){ pagination = this.pagination; }

    if (folderId) {
      this.loadingFolders[folderId] = true;
    } else {
      this.isLoadingCollections = true;
    }

    const payload = this.buildCollectionsPayload(folderId, pagination);

    const getCollections$ = this.collectionService.getCollections(payload)
      .pipe(take(1), finalize(() => {
        if (folderId) {
          this.loadingFolders[folderId] = false;
        } else {
          this.isLoadingCollections = false;
        }
      })).subscribe({
      next: (data) => {
        if (folderId) {
          this.openFolders[folderId].collections = [...this.openFolders[folderId].collections, ...data.result_collections];
          this.openFolders[folderId].pagination = data.page;
        } else {
          this.collections = [
            ...this.collections,
            ...data.result_collections
          ];
          this.pagination = data.page;
        }
      },
      error: (error) => {
        console.error(error);
      }
    });
    this.subscriptions.add(getCollections$);
  }

  private filterCollections() {
    this.currentFolder = null;
    this.currentSavingCollectionId = null;
    this.pagination.current_page = 1;
    this.collections = [];
    this.loadCollections(null);
  }

  private filterFolders() {
    this.currentFolder = null;
    this.currentSavingCollectionId = null;
    this.folders = [];
    this.loadFolders(null);
  }

  private filterSharedCollections() {
    this.currentFolder = null;
    this.currentSavingCollectionId = null;
    this.sharedPagination.current_page = 1;
    this.sharedCollections = [];
    this.loadSharedCollections(null);
  }

  private filterSharedFolders() {
    this.currentFolder = null;
    this.currentSavingCollectionId = null;
    this.sharedFolders = [];
    this.loadSharedFolders(null);
  }

  private isMonitorService(): boolean {
    return this.storeService.isMonitorSearch();
  }

  private buildCollectionPayload(): Collection {
    const payload = {
      name: this.collectionName,
      collection_type: this.patentListScope
    } as Collection;

    if (this.isMonitorService()) {
      payload.monitor_run_id = this.monitorStoreService.selectedMonitorRun;
    } else {
      payload.search_hash = this.storeService.originalSearchHash ?? this.storeService.searchHash;
    }

    const freeTextQuery = this.storeService.getAppliedFiltersQuery();

    if (freeTextQuery) {
      payload['free_text_query'] = freeTextQuery;
    }

    if (this.isFamilyCollectionType) {
      payload.document_ids = this.getStoredDocumentIds();
    } else {
      payload.publication_numbers = this.storeService.selectedPublications;
    }

    if (this.currentFolder && this.currentFolder.id) {
      payload.folder_id = this.currentFolder.id;
    }
    return payload;
  }

  onLoadMoreClicked() {
    this.nextPageCollectionsSubject.next(true);
  }

  onLoadMoreSharedClicked(){
    this.nextPageSharedCollectionsSubject.next(true);
  }

  onNestedLoadMoreClicked(isShared: boolean, folderId: number) {
    const nf = this.openFolders[folderId];
    if(!nf) return;
    nf.pagination.current_page += 1;
    if(isShared){
      this.loadSharedFolders(folderId);
      this.loadSharedCollections(folderId, nf.pagination);
    } else {
      this.loadFolders(folderId);
      this.loadCollections(folderId, nf.pagination);
    }
  }
}
