import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  Renderer2,
  SimpleChanges, TemplateRef,
  ViewChild,
  ViewContainerRef
} from '@angular/core';
import { TeamUser, UserService, UserTitlePipe } from '@core';
import { Subscription } from 'rxjs';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';

/**
 * Todo:
 * Need to clean up the size of the avatars, just use the size property.
 * Also remove the avatarSize and avatarFontSize properties
 */
@Component({
  selector: 'app-user-avatar',
  templateUrl: './user-avatar.component.html',
  styleUrls: ['./user-avatar.component.scss']
})
export class UserAvatarComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  @Input() user: TeamUser;
  @Input() hasSubTitle = true;
  @Input() subTitleCssClass: string = '';
  @Input() hasCustomAvatar = false;
  @Input() showTooltip = true;
  @Input() showYouSuffix = false;
  @Input() avatarBlob = null;
  @Input() refreshAvatar = false;
  @Input() subtitleMaxWidth: string = null;
  @Input() tooltipPrefix: string = null;
  @Input() size: 'xxxsmall' | 'xxsmall' | 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge' | 'xxlarge' | 'huge' | 'xhuge' = 'xsmall';
  @Input() avatarSize: number = null;
  @Input() avatarFontSize: number = null;
  @Input() showTruncatableTooltip: boolean = true;
  @Input() popoverUserTemplate: TemplateRef<any> = null;
  @Input() userExtraInfoTemplate: TemplateRef<any> = null;
  @Input() isActive: boolean = false;
  @Input() showUserTitleOnTooltip: boolean = true;
  @Input() showUserEmailOnTooltip: boolean = true;
  @Input() avatarTooltipCss: string = 'white-popover popover-user-avatar';
  @Input() autoCloseAvatarTooltip: boolean = true;

  @Output() hasAvatar = new EventEmitter<boolean>(true);
  @Output() avatarTooltipShown = new EventEmitter<NgbPopover>();
  @Output() avatarTooltipHidden = new EventEmitter<NgbPopover>();

  @ViewChild('avatarContainerEle', {read: ElementRef, static: false}) avatarContainerEle: ElementRef<HTMLDivElement>;
  @ViewChild('avatarEle', {read: ElementRef, static: false}) avatarEle: ElementRef<HTMLDivElement>;

  avatarDataUrl = null;

  private subscriptions = new Subscription();

  constructor(
    private readonly viewRef: ViewContainerRef,
    private readonly renderer: Renderer2,
    private userService: UserService,
    private userTitlePipe: UserTitlePipe
  ) {
  }

  get isUserData(): boolean {
    if (this.user) {
      const attributes = Object.keys(this.user);
      return ['id', 'email'].every((v) => attributes.includes(v));
    }

    return false;
  }

  get isMe(): boolean {
    return this.userService.isMe(this.user);
  }

  ngOnInit(): void {
    if (this.avatarSize) {
      this.viewRef.element.nativeElement.style.setProperty('--avatarSize', this.avatarSize + 'px');
    }

    if (this.avatarFontSize) {
      this.viewRef.element.nativeElement.style.setProperty('--avatarFontSize', this.avatarFontSize + 'px');
    }
  }

  ngAfterViewInit() {
    this.lazyRequestUserImage();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.avatarBlob) {
      if (changes.avatarBlob.currentValue) {
        this.loadImageFromBlob(changes.avatarBlob.currentValue);
        this.hasAvatar.emit(true);
      } else {
        this.loadImageFromBlob(null);
        this.hasAvatar.emit(false);
      }
    }

    if (changes.refreshAvatar?.currentValue) {
      if (this.avatarBlob) {
        this.loadImageFromBlob(this.avatarBlob);
        this.hasAvatar.emit(true);
      } else {
        this.lazyRequestUserImage();
      }
    }
  }

  getTextAvatarTitle(): string {
    const title = this.getUserTitle();

    if (this.user?.more_users) {
      return title;
    }

    const nameParts = title.split(' ').filter((n) => n && n.trim().length > 0);
    if (nameParts.length > 0) {
      const firstChar = nameParts[0][0];

      if (nameParts.length > 1) {
        return firstChar + nameParts[1][0];
      }

      if (nameParts[0].length > 1) {
        return firstChar + nameParts[0][1];
      }
    }

    return 'UN';
  }

  private lazyRequestUserImage() {
    if (!this.avatarBlob && this.isUserData) {
      if (this.user?.id) {
        if (this.canLazyExecute() && !this.userService.isAvatarCached(this.user?.id)) {
          const obs = new IntersectionObserver(entries => {
            entries.forEach(entry => {
              if (entry.isIntersecting && entry.intersectionRatio > 0 && entry.boundingClientRect.height > 0) {
                this.requestUserImage();
                obs.unobserve(this.avatarContainerEle.nativeElement);
                obs.disconnect();
              }
            });
          }, {threshold: 0.2});
          obs.observe(this.avatarContainerEle.nativeElement);
        } else {
          this.requestUserImage();
        }
      } else {
        this.loadImageFromBlob(null);
        this.hasAvatar.emit(false);
      }
    }
  }

  private requestUserImage() {
    if (this.avatarDataUrl) {
      this.updateBackgroundImage(this.avatarDataUrl);
      return;
    }

    const getAvatar$ = this.userService.getAvatar(this.user?.id).subscribe({
      next: (val) => {
        this.loadImageFromBlob(val);
        this.hasAvatar.emit(true);
      },
      error: error => {
        this.loadImageFromBlob(null);
        this.hasAvatar.emit(false);
      }
    });
    this.subscriptions.add(getAvatar$);
  }

  private getUserTitle(): string {
    const title = this.userTitlePipe.transform(this.user);
    return title ?? 'Unknown user';
  }

  private createImageUrl(data: Blob | File): string {
    if (!data) {
      return null;
    }

    const creator = window.URL || window.webkitURL;
    return creator.createObjectURL(data);
  }

  private loadImageFromBlob(data: Blob | File) {
    const imageUrl = this.createImageUrl(data);
    setTimeout(() => {
      this.updateBackgroundImage(imageUrl);
    }, 200);
  }

  private updateBackgroundImage(imageUrl: string) {
    if (this.avatarEle?.nativeElement) {
      this.renderer.setStyle(this.avatarEle?.nativeElement, 'backgroundImage', imageUrl ? `url(${imageUrl})` : '');
    }
    this.avatarDataUrl = imageUrl;
  }

  private canLazyExecute() {
    return window && 'IntersectionObserver' in window;
  }
}
