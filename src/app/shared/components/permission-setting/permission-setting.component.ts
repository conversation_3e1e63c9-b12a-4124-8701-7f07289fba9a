import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UserService } from '@core/services';
import { Subject, takeUntil } from 'rxjs';
import { finalize, take } from 'rxjs/operators';
import { PopperComponent } from '@shared/components/popper/popper.component';

@Component({
  selector: 'app-permission-setting',
  templateUrl: './permission-setting.component.html',
  styleUrl: './permission-setting.component.scss'
})
export class PermissionSettingComponent implements OnInit, OnDestroy {
  @Input() permissionKey: string = UserService.DEFAULT_COMMENT_PERMISSION_KEY;
  @Input() footerText: string = 'Permission settings can be adjusted for each individual comment.';
  @Input() popper: PopperComponent = null;
  @Output() permissionSettingChanged = new EventEmitter<boolean>();

  isSaving = false;
  defaultPermission: boolean;

  private destroy$ = new Subject<void>();

  constructor(
    private userService: UserService
  ) {
  }

  ngOnInit() {
    this.setDefaultPermission();

    this.userService.permissionSettingChanged$
      .pipe(
        takeUntil(this.destroy$)
      )
      .subscribe({
        next: (settings) => {
          this.setDefaultPermission();
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onPermissionItemClicked(value: boolean) {
    if (!this.permissionKey) {
      console.warn('Permission key is not set');
      return;
    }

    this.isSaving = true;
    this.userService.updatePermissionSetting(this.permissionKey, value)
      .pipe(
        take(1),
        takeUntil(this.destroy$),
        finalize(() => {
          if (this.popper) {
            this.popper.hide();
          }
          this.isSaving = false;
        })
      )
      .subscribe({
        next: (user) => {
          this.permissionSettingChanged.emit(value);
        },
        error: (error) => {
          console.error(error);
        }
      });
  }

  private setDefaultPermission() {
    this.defaultPermission = this.userService.getUISetting(this.permissionKey, true);
  }
}
