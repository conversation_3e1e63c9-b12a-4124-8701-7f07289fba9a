<div class="inline-modal-container permission-setting-container">
  <div class="inline-modal-block">
    <div class="content-heading-h6 content-color-secondary p-b-spacing-sm">Default settings</div>
    <div class="w-100">
      <div
        class="figma-dropdown-item figma-dropdown-item-hover p-x-spacing-sm p-y-spacing-x-s d-flex align-items-center justify-content-between gap-spacing-sm"
        (click)="onPermissionItemClicked(false)"
        [class.active]="!defaultPermission" [class.disabled]="isSaving">
        <div class="d-flex flex-column align-items-start justify-content-start gap-spacing-xxx-s">
          <div class="content-heading-h6 d-flex align-items-center gap-spacing-xx-s">
            <i class="fa-regular fa-globe"></i>
            <div>Public</div>
          </div>
          <div class="content-body-xsmall content-color-tertiary">Everyone in your company</div>
        </div>
        <i class="fa-regular fa-check" [class.invisible]="defaultPermission"></i>
      </div>
      <div
        class="figma-dropdown-item figma-dropdown-item-hover p-x-spacing-sm p-y-spacing-x-s d-flex align-items-center justify-content-between gap-spacing-sm"
        (click)="onPermissionItemClicked(true)"
        [class.active]="defaultPermission" [class.disabled]="isSaving">
        <div class="d-flex flex-column align-items-start justify-content-start gap-spacing-xxx-s">
          <div class="content-heading-h6 d-flex align-items-center gap-spacing-xx-s">
            <i class="fa-regular fa-lock"></i>
            <div>Private</div>
          </div>
          <div class="content-body-xsmall content-color-tertiary">Only you</div>
        </div>
        <i class="fa-regular fa-check" [class.invisible]="!defaultPermission"></i>
      </div>
    </div>
  </div>

  <ng-container *ngIf="footerText?.length">
    <div class="popover-divider"></div>

    <div class="inline-modal-block flex-row gap-spacing-xx-s">
      <i class="far fa-info-circle"></i>
      <div class="content-body-xsmall text-left">
        {{ footerText }}
      </div>
    </div>
  </ng-container>
</div>
