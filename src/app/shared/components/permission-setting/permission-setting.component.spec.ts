import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PermissionSettingComponent } from './permission-setting.component';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

describe('PermissionSettingComponent', () => {
  let component: PermissionSettingComponent;
  let fixture: ComponentFixture<PermissionSettingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [PermissionSettingComponent],
      imports: [SharedModule, HttpClientTestingModule, RouterModule.forRoot([])],
      providers: [
        provideMatomo({
          siteId: '7',
          trackerUrl: 'https://stats.dennemeyer.digital/',
          disabled: true
        })
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PermissionSettingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
