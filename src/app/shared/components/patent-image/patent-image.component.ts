import { Component, EventEmitter, Input, OnChanges, OnDestroy, Output, SimpleChanges } from '@angular/core';
import { PatentService, PatentViewService } from '@core/services';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import lgZoom from 'lightgallery/plugins/zoom';
import lgThumbnail from 'lightgallery/plugins/thumbnail';
import lgRotate from 'lightgallery/plugins/rotate';
import lgFullscreen from 'lightgallery/plugins/fullscreen';
import lgPager from 'lightgallery/plugins/pager';
import lgAutoplay from 'lightgallery/plugins/autoplay';
import lgShare from 'lightgallery/plugins/share';
import { LightGallery } from 'lightgallery/lightgallery';

declare var $: any;

@Component({
  selector: 'app-patent-image',
  templateUrl: './patent-image.component.html',
  styleUrls: ['./patent-image.component.scss']
})
export class PatentImageComponent implements OnChanges, OnD<PERSON>roy {
  @Input() publication?: string;
  @Input() patent?: any;
  @Input() image?: string;
  @Input() isLoadingImage = true;
  @Input() selectedImageIndex = 0;
  @Input() loadImageList = true;
  @Input() isSmallIcon?: boolean;
  @Input() showImageSource = true;

  @Output() isLoadingImageChanged = new EventEmitter<boolean>(false);

  /**
   * container for patent number for image reference label
   */
  public referencePatentNumber?: string;

  public images: Array<any> = [];
  public widthImg = {};
  public errors: Array<string>;
  public shouldShowLightGallery = false;

  private needToShowGallery = false;
  private subscriptions = new Subscription();
  private lightGallery!: LightGallery;
  settings = {
    thumbnail: true,
    dynamic: true,
    html: true,
    index: this.selectedImageIndex,
    dynamicEl: this.patent?.images || [],
    plugins: [lgZoom, lgThumbnail, lgFullscreen, lgPager, lgRotate, lgShare, lgAutoplay]
  };

  constructor(private patentService: PatentService, public patentViewService: PatentViewService) {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['patent']) {
      if (this.patent && this.patent['general'].obfuscated) {
        this.setLoadingImage(false);
        this.image = null;
        return;
      }
      this.loadMainImage();
      if (this.loadImageList) {
        this.loadListImages();
      }
    }
  }

  get patentViewerLink(): string{
    return this.patentViewService.getPatentViewerBaseUrl(this.patent.general.docdb_family_id, this.publication);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  showGallery() {
    this.shouldShowLightGallery = true;
    
    if (!this.patent.images) {
      this.needToShowGallery = true;
      this.loadListImages();
      return;
    }

    setTimeout(() => {
      if (this.lightGallery) {
        this.lightGallery.refresh(this.patent.images);
        this.lightGallery.openGallery(this.selectedImageIndex);
      }
    });
  }

  onAfterOpen = () => {
    this.needToShowGallery = false;
    this.setLoadingImage(false);
  };

  onBeforeClose = () => {
    setTimeout(() => {
      this.shouldShowLightGallery = false;
    }, 100);
  };

  onBeforeSlide = (detail): void => {
    const newSlideIndex: number = detail.index;

    if (!this.patent.images[newSlideIndex]) {
      return;
    }
    if (!this.patent.images[newSlideIndex].loadedHighQuality && this.patent.images[newSlideIndex].path) {
      const payload = this.getImagePayload();
      payload['path'] = this.patent.images[newSlideIndex].path;
      const getPatentImage$ = this.patentService.getPatentImage(payload)
        .subscribe({
          next: ({data}) => {
            if (data && data.bytes) {
              const imgData = 'data:image/png;base64,' + data.bytes;
              this.patent.images[newSlideIndex].src = imgData;
              this.patent.images[newSlideIndex].downloadUrl = imgData;
              this.patent.images[newSlideIndex].loadedHighQuality = true;
              this.patent.images[newSlideIndex].patentNumber = data['source-document'];
            }
            this.lightGallery.updateSlides(this.patent.images, newSlideIndex);
          },
          error: (err) => { console.error(err); }
        });
      this.subscriptions.add(getPatentImage$);
    }
  };

  onInit = (detail): void => {
    this.lightGallery = detail.instance;
  };

  private loadMainImage() {
    if (!this.patent) {
      return;
    }

    if (this.patent.mainImage) {
      this.image = this.patent.mainImage;
      this.setLoadingImage(false);
      this.referencePatentNumber = this.patent.imageReferencePatentNumber;
      return;
    }

    this.errors = [];

    this.setLoadingImage(true);

    const getPatentImage$ = this.patentService.getPatentImage(this.getImagePayload())
      .pipe(finalize(() => {
        this.setLoadingImage(false);
      }))
      .subscribe({
        next: ({data}) => {
          if (data && data.bytes) {
            const imgData = 'data:image/png;base64,' + data.bytes;
            this.image = imgData;
            this.patent.mainImage = imgData;
            this.patent.imageReferencePatentNumber = data['source-document'];
            this.referencePatentNumber = data['source-document'];
          }
        },
        error: (err) => {
          this.image = null;
          this.patent.mainImage = null;
          this.errors = [err.error.message];
        }
      });
    this.subscriptions.add(getPatentImage$);
  }

  private loadListImages() {
    if (!this.patent || this.patent.images) {
      return;
    }
    this.setLoadingImage(true);

    const getListImage$ = this.patentService.getListImage(this.getImagePayload())
      .subscribe({
        next: (list) => {
          const attachments = [];

          for (const attachment of list.attachments) {
            for (const item of attachment.attachments) {
              if (item.image && item.image.bytes) {
                const image64 = 'data:image/png;base64,' + btoa(atob(item.image.bytes));
                attachments.push({
                  path: item.path,
                  downloadUrl: image64,
                  src: image64,
                  patentNumber: attachment.patent_number,
                  thumb: image64,
                  subHtml: this.getLabelReferenceGallery(attachment.patent_number)
                });
              }
            }
          }

          if (attachments.length) {
            this.patent.images = attachments;
          } else {
            const img64 = this.patent.mainImage;
            this.patent.images = img64 ? [{
              downloadUrl: img64,
              src: img64,
              patentNumber: this.patent.general.raw_publication_number,
              thumb: img64,
              subHtml: this.getLabelReferenceGallery(this.patent.general.raw_publication_number)
            }] : [];
          }

          if (this.needToShowGallery) {
            this.showGallery();
            this.needToShowGallery = false;
          } else {
            this.setLoadingImage(false);
          }
        },
        error: (err)=> {
          this.setLoadingImage(false);
          this.errors = [err.error.message];
          console.error(err);
        }
      });
    this.subscriptions.add(getListImage$);
  }

  getLabelReferenceGallery(patentNumber: string) {
    return `<div style="font-size: 10px">Drawing from ${patentNumber}</div>`;
  }

  getSourceImageLabel() {
    if (this.selectedImageIndex <= 0 && this.referencePatentNumber) {
      return 'Front-page drawing from ' + this.referencePatentNumber;
    }

    if (this.selectedImageIndex > -1 && this.patent.images && this.patent.images.length > this.selectedImageIndex) {
      return 'Drawing from ' + this.patent.images[this.selectedImageIndex].patentNumber;
    }
    return '';
  }

  private setLoadingImage(val: boolean) {
    this.isLoadingImage = val;
    this.isLoadingImageChanged.next(val);
  }

  private getImagePayload() {
    return this.publication ? {patent_number: this.publication} : {document_id: this.patent?.general?.docdb_family_id};
  }

  openNewWindow(){
    const left = (screen.width / 2) - (1024 / 2);
    const top = (screen.height / 2) - (768 / 2);
    window.open(`${this.patentViewerLink}?gallery_index=${this.selectedImageIndex}`, '_blank', `popup,top=${top},left=${left},width=1024,height=768`);
  }
}
