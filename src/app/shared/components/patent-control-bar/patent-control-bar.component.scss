@use 'scss/figma2023/variables' as variable;

.similar-search-top {
  @import 'scss/components/input-group';

  clear: both;

  .separator {
    height: 35px;
  }

  .input-group {
    width: 320px;
    .has-filter-term-error {
      padding-left: 25px;
    }
  }

  .close {
    font-size: 21px;
  }

  .btn-save {
    min-width: 55px !important;
  }
}
.icon{
  &-octi-ai {
    i {
      width: 0.875rem;
      height: 1rem;
      background-position: 0;
      background-size: 0.875rem;
      background-repeat: no-repeat;
      transition: all 0.2s ease;
      background-image: url('/assets/images/octi_ai/octi_thinking_focus.svg');
    }

    &:hover, &.active {
      i {
        background-image: url('/assets/images/octi_ai/octi_surprised_focus.svg');
      }
    }
  }
}

::ng-deep {
  .filter-term-error {
    color: #d8000c;
    position: absolute;
    left: 5px;
    top: 7px;
    z-index: 99;
  }

  div[role="tooltip"].popper-instance.white-tooltip.p-spacing-none .button-rating-form-popper {
    min-width: 347px !important;
    max-width: 347px !important;
  }

  .tags-select-patent-control-bar {
    min-width: 21.75rem;
    max-width: 21.75rem;
  }
}

.input-group {
  .checkbox-wrap{
    bottom: -33px;
    span{
      font-size: .875rem;
      padding-left: 25px;

    }
  }
}

.task-create-button {
  &:hover, &:active, &:focus {
    &:not(:disabled) .task-create-button-icon {
      background: url(/assets/images/tool-icon-task-white.svg);
    }
  }
  .task-create-button-icon {
    display: inline-block;
    width: 15px;
    height: 15px;
    background: url(/assets/images/tool-icon-task-default.svg);
  }
}

.group {
  &.group-separator-right {
    border-right: 1px solid #d5d5d5;
    border-image: linear-gradient(0deg, white, #d5d5d5, white) 1;
  }
  .group-name {
    color: #e3e3e3;
    font-size: 0.8rem;
  }
}

.octi-ai-bottom{
  &-wrapper{
    position: fixed;
    bottom: 76px;
    right: 20px;
  }
  &-launcher{
    position: fixed;
    padding: 0;
    margin: 0;
    border: 1px solid variable.$colour-blue-brand-500;
    bottom: 76px;
    right: 24px;
    max-width: 46px;
    width: 46px;
    max-height: 46px;
    height: 46px;
    border-radius: 50%;
    background: variable.$colours-background-bg-primary;
    cursor: pointer;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06), 0 2px 32px 0 rgba(0, 0, 0, 0.16);
    transition: transform 167ms cubic-bezier(0.33, 0.00, 0.00, 1.00);
    box-sizing: content-box;
    &::before{
      content: "";
      background-image: url('/assets/images/octi_ai/octi_thinking_focus.svg');
      background-repeat: no-repeat;
      background-position: center;
      display: inline-block;
      background-size: 24px;
      width: 24px;
      height: 24px;
      margin: variable.$spacing-system-spacing-md;
      transition: all ease-in-out .2s;
    }
    &:hover::before{
      background-image: url('/assets/images/octi_ai/octi_surprised_focus.svg');
    }
  }
}

.octi-ai-bottom-wrapper{
  &.ip-lounge-user {
    bottom: 110px !important;
    .octi-ai-bottom-launcher {
      bottom: 110px !important;
    }
  }
}

:host {
  width: 100% !important;
}
