import {
  AfterViewInit,
  Component, ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { catchError, debounce, debounceTime, finalize, switchMap } from 'rxjs/operators';
import { Subscription, of, filter, tap, ReplaySubject, timer } from 'rxjs';
import { NgbDropdown, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import {
  ApplicantsAliasesService,
  BooleanSearchService,
  CitationSearchService,
  Collection,
  CollectionService,
  MatomoService,
  MonitorService,
  PatentListScopeEnum,
  SearchTermService,
  SemanticSearchService,
  SortBySimilarityParam,
  UserService,
  ToastService,
  ToastTypeEnum,
  RatingService
} from '@core/services';
import { BaseStoreService, CollectionStoreService, SortParams } from '@core/store';
import { CollaborationPermissionEnum, CollaborationResourceTypeEnum, TaskModel, TaskResourceTypeEnum, User } from '@core/models';
import { ShareDialogComponent } from '../share-dialog/share-dialog.component';
import { HarmonizeApplicantsComponent } from '../harmonize-applicants/harmonize-applicants.component';
import { AddToCollectionComponent } from '../add-to-collection/add-to-collection.component';
import { ExportDialogComponent } from '../export-dialog/export-dialog.component';
import {
  ColSelectorComponent,
  FilterListDialogComponent,
  ShareDialogResult,
  SortBySimilarityComponent,
  TaskFormComponent
} from '@shared/components';
import { ViewModeTypeEnum } from '@search/patent/types';
import { PopperComponent } from '../popper/popper.component';
import { ShowRatingsEventParams } from '@patent/patent-ratings/shared/types';

enum ControlBarItemEnum {
  REFINE_SEARCH = 'REFINE_SEARCH',
  REMOVE_ADD_PATENTS = 'REMOVE_ADD_PATENTS',
  OCTI_AI = 'OCTI_AI',
  REQUEST_RATINGS = 'REQUEST_RATINGS',
  ADD_TAG = 'ADD_TAG',
  CUSTOMIZE_COLUMNS = 'CUSTOMIZE_COLUMNS',
  GROUP_APPLICANTS = 'GROUP_APPLICANTS',
  SORT_BY_SIMILARITY = 'SORT_BY_SIMILARITY',
  FILTER = 'FILTER',
  LANDSCAPE = 'LANDSCAPE',
  MONITOR = 'MONITOR',
  SAVE = 'SAVE',
  EXPORT = 'EXPORT',
  SHARE = 'SHARE'
}

@Component({
  selector: 'app-patent-control-bar',
  templateUrl: './patent-control-bar.component.html',
  styleUrls: ['./patent-control-bar.component.scss'],
  providers: [
    NgbDropdown
  ]
})
export class PatentControlBarComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('ratingPopper') ratingPopper: PopperComponent;
  @ViewChild('octiAIPopper') octiAIPopper: PopperComponent;
  @ViewChild('addTagPopper') addTagPopper: PopperComponent;

  @Input() saveSearchTextInput: string;
  @Input() saveSearchNameReadOnly: boolean;
  @Input() saveSearchHeadline: string;
  @Input() saveSearchHideAgreementCheck: boolean;
  @Input() resource: Collection;
  @Input() aggregate: boolean;
  @Input() columnsToShow: any[] = [];
  @Input() defaultSelectedColumnsToShow: string[];
  @Input() harmonizeApplicants = [];

  emptyTask: TaskModel = {
    id: null,
    author_id: null,
    resource_id: null,
    resource_type: null,
    task_type: null,
    subject: null,
    description: null,
    deadline: null,
    assignees: [],
    topics: [],
    document_id: null,
    status: null
  };

  private _hasHarmonizeControl = false;
  get hasHarmonizeControl(): boolean {
    return this._hasHarmonizeControl;
  }
  @Input() set hasHarmonizeControl(value: boolean) {
    this._hasHarmonizeControl = value;
    this.getHarmonizeApplicants();
  };

  @Input() hasAddToSearchControl = false;
  @Input() hasExportControl = false;
  @Input() hasTemporaryLinkControl = false;
  @Input() isShared?: boolean;
  @Input() sharedTooltip?: string;
  @Input() hasShowColumnControl = true;
  @Input() searchService: SemanticSearchService | CitationSearchService | BooleanSearchService | CollectionService | MonitorService;
  @Input() exportDisplayOptions: Array<string> = ['pdf', 'csv', 'xlsx'];
  @Input() hasSaveToCollectionControl = false;
  @Input() exportAdditionalParams: Object;
  @Input() saveSearchResourceId: number;
  @Input() hasSortBySimilarity: boolean;
  @Input() taskShowCreationButton: boolean = false;
  @Input() taskResourceId: number;
  @Input() taskResourceType: TaskResourceTypeEnum;
  @Input() hasFilterListControl: boolean;
  @Input() filterTitle: string = 'Filter result list';
  @Input() hasAddPatent: boolean;
  @Input() hasMonitor: boolean;
  @Input() hasLandscape: boolean;
  @Input() storeService: BaseStoreService;
  @Input() hasAddTagControl = true;
  @Input() patentListScope: PatentListScopeEnum = PatentListScopeEnum.FAMILY;
  @Input() hasOctiAIControl = false;
  @Input() permission: CollaborationPermissionEnum = CollaborationPermissionEnum.READ_WRITE;

  @Output() export: EventEmitter<string> = new EventEmitter();
  @Output() sharePatentsEvent: EventEmitter<any> = new EventEmitter();
  @Output() closeShareEvent: EventEmitter<any> = new EventEmitter();
  @Output() sortBySimilarity: EventEmitter<any> = new EventEmitter();
  @Output() filterListEvent: EventEmitter<any> = new EventEmitter();
  @Output() taskSaved = new EventEmitter<{ message: string, payload: TaskModel, savedTasks: TaskModel[] }>();
  @Output() addPatent: EventEmitter<any> = new EventEmitter();
  @Output() monitor: EventEmitter<any> = new EventEmitter();
  @Output() landscape: EventEmitter<any> = new EventEmitter();

  @ViewChild('colSelector', {static: false}) colSelector: ColSelectorComponent;

  selectedPublications = [];
  showHarmonize = false;

  selectedColumnsToShow = [];

  user: User;

  hasTeam = false;

  controlBarItemEnum = ControlBarItemEnum;
  collapsedControlBarItems: ControlBarItemEnum[] = [];

  private columnsToShowKey = 'columns_to_show';
  private subscriptions = new Subscription();
  private calculateCollapsedItemsSubject = new ReplaySubject<number>(1);
  private resizeObserver: ResizeObserver = null;

  constructor(
    public userService: UserService,
    private searchTermService: SearchTermService,
    private collectionsStoreService: CollectionStoreService,
    private modalService: NgbModal,
    private matomoService: MatomoService,
    private applicantsAliasesService: ApplicantsAliasesService,
    private toastService: ToastService,
    private ratingService: RatingService,
    private el: ElementRef
  ) {
  }

  get sortBySimilarityValue() {
    return this.storeService.sortBySimilarity;
  }

  get isFilteredList(): boolean {
    return this.storeService.advancedFilterAppliedQuery?.length > 0;
  }

  get isPatentSelected(): boolean {
    return this.storeService.selectedPatentIds.length > 0;
  }

  get searchHash(): string {
    return this.storeService.searchHash;
  }

  get octiListChatRange(): string{
    const start = ((this.storeService.pagination.current_page-1)*this.storeService.pagination.page_size)+1;
    const end = this.storeService.pagination.current_page*this.storeService.pagination.page_size;
    const postFix = (end-start)>25 ? '(top 25)':'';
    return `${start} - ${end>this.storeService.pagination.total_hits ?this.storeService.pagination.total_hits: end} ${postFix}`;
  }

  get isIpLounge(): boolean {
    return this.userService.isIpLounge();
  }

  get collapsedItemsButton(): HTMLElement {
    return document.getElementById('collapsed-items-button');
  }

  @HostListener('window:resize')
  onWindowResize() {
    this.calculateCollapsedItemsSubject.next(100);
    this.colSelector?.close();
  }

  ngOnInit() {
    this.user = this.userService.getUser();
    this.filterValidColumns();

    const selectedPublications$ = this.storeService.selectedPublications$.subscribe({
      next: publications => {
        this.selectedPublications = publications;
        this.calculateCollapsedItemsSubject.next(200);
      }
    });
    this.subscriptions.add(selectedPublications$);

    const patentListViewMode$ = this.storeService.patentListViewMode$.subscribe({
      next: (mode) => {
        if (mode !== ViewModeTypeEnum.ANALYSIS) {
          const selectedColumns = this.getColumnsToShowByViewMode(mode);
          this.setSelectedColumnsToShowByViewMode(selectedColumns);
          this.addSimilarityColumnToShow(this.sortBySimilarityValue);
          this.calculateCollapsedItemsSubject.next(100);
        }
      }
    });
    this.subscriptions.add(patentListViewMode$);

    const sortBySimilarity$ = this.storeService.sortBySimilarity$.subscribe({
      next: (val) => {
        this.addSimilarityColumnToShow(val);
        this.calculateCollapsedItemsSubject.next(100);
      }
    });
    this.subscriptions.add(sortBySimilarity$);

    const getTeamUsers$ = this.userService.getTeamUsers({page_size: 1}).subscribe({
      next: (data) => {
        this.hasTeam = !!data.users.length;
      }
    });
    this.subscriptions.add(getTeamUsers$);

    const searching$ = this.storeService.searching$.subscribe({
      next: value => {
        this.calculateCollapsedItemsSubject.next(100);
      }
    });
    this.subscriptions.add(searching$);

    const selectedPatentIds$ = this.storeService.selectedPatentIds$.subscribe({
      next: value => {
        this.calculateCollapsedItemsSubject.next(200);
      }
    });
    this.subscriptions.add(selectedPatentIds$);

    const calculateCollapsedItems$ = this.calculateCollapsedItemsSubject
      .pipe(
        debounce((delay) => timer(delay ?? 0)),
      )
      .subscribe({
        next: val => this.calculateCollapsedItems()
      });
    this.subscriptions.add(calculateCollapsedItems$);

    this.getHarmonizeApplicants();
    this.detectToolbarWidthChanged();
  }

  ngAfterViewInit() {
    this.calculateCollapsedItemsSubject.next(1000);
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.clearSuccessMessages();
    this.destroyResizeObserver();
  }

  private detectToolbarWidthChanged() {
    this.resizeObserver = new ResizeObserver((entries) => {
      this.calculateCollapsedItemsSubject.next(100);
    });

    this.resizeObserver.observe(this.el.nativeElement);
  }

  private destroyResizeObserver() {
    if (this.resizeObserver) {
      this.resizeObserver.unobserve(this.el.nativeElement);
      this.resizeObserver.disconnect();
    }
  }

  private filterValidColumns() {
    // Check if column require company
    if (!this.user?.profile?.company_id) {
      this.columnsToShow = this.columnsToShow.filter( column => !column['requireCompany'])
    }
  }

  private getHarmonizeApplicants() {
    if (this.hasHarmonizeControl && this.storeService) {
      const searchHash$ = this.storeService?.searchHash$?.pipe(
        filter(hash => !!hash),
        debounceTime(1000),
        switchMap((hash) => {
          return this.searchTermService.search({field: 'applicants', search_hash: hash})
            .pipe(
              catchError((err) => of({data: null})),
              tap(({data}) => {
                if (data) {
                  this.harmonizeApplicants = data.results;
                  this.storeService.applicantsByHash = data.results;
                }
              })
            );
        })
      ).subscribe();
      this.subscriptions.add(searchHash$);
    }
  }

  getDocuments(){
    if(!this.storeService || !this.searchService) return;
    let docs: any[];
    if(this.storeService instanceof CollectionStoreService){
      docs = this.storeService.collectionDocuments;
    } else {
      docs = this.searchService.getDocuments()
    }
    return docs;
  }

  openSharePatents(event, stopSharing: boolean) {
    if(this.userService.canUseSaveAndShareFeature('share')){
      if (!stopSharing) {
        this.matomoService.resultListShareButton();
      }
      this.clearSuccessMessages();
      this.showSharePatentsDialog(stopSharing);
    }
    (event.target as HTMLElement).blur();
  }

  addNumbersToSearch() {
    this.matomoService.resultListAddToSearchButton();
    this.storeService.addNumberToSearch = this.selectedPublications;
  }

  enableToAdd() {
    const newSelection = this.selectedPublications.filter(number => this.storeService.typedPublications.indexOf(number) === -1);
    if (!newSelection.length) {
      return false;
    }

    return newSelection.length + this.storeService.typedPublications.length <= 5;
  }

  selectedColumnsToShowEvent(event) {
    const columnsToSave = event.filter(x => !('temporary' in x)).map((v) => v.property);
    const viewMode = this.storeService.patentListViewMode || ViewModeTypeEnum.COMBINED;
    const storeServiceName = this.getStoreServiceName();
    const allColumnsToShow = this.userService.getUISetting(this.columnsToShowKey, null);
    let columnsSetting = allColumnsToShow || {};
    if (allColumnsToShow && storeServiceName in allColumnsToShow) {
      columnsSetting[storeServiceName][viewMode] = columnsToSave;
    } else {
      columnsSetting[storeServiceName] = {
        [viewMode]: columnsToSave
      };
    }

    const settingsToUpdate = {[this.columnsToShowKey]: columnsSetting};

    const updateUISettings$ = this.userService.updateUISettings(settingsToUpdate).subscribe({
      next: () => this.storeService.selectedColumnsToShow = event
    });
    this.subscriptions.add(updateUISettings$);
  }

  onHarmonize() {
    this.showHarmonize = !this.showHarmonize;
    let isChanged = false;
    const modal = this.modalService.open(HarmonizeApplicantsComponent, {size: 'lg'});
    modal.componentInstance.applicants = this.harmonizeApplicants;
    modal.componentInstance.storeService = this.storeService;
    const hasBeenChanged$ = modal.componentInstance.hasBeenChanged.subscribe({
      next: (val) => {
        isChanged = val;
      }
    });
    this.subscriptions.add(hasBeenChanged$);
    modal.result.then(({data}) => {
      if (isChanged) {
        this.applicantsAliasesService.changeApplicantsEvent.emit(true);
      }
    }, reason => {
      if (isChanged) {
        this.applicantsAliasesService.changeApplicantsEvent.emit(true);
      }
    });
  }

  onSaveToCollectionClicked(event) {
    if(this.userService.canUseSaveAndShareFeature('save')){
    this.matomoService.resultListSaveButton();
    this.showSaveToCollection();
    }
    (event.target as HTMLElement).blur();
  }

  canSaveToCollection(): boolean {
    const user = this.user;
    return user && this.userService.isNotExternalUser() && !this.aggregate && this.hasSaveToCollectionControl;
  }

  canAddNumbersToSearch() {
    return this.userService.isNotExternalUser() && !this.aggregate && this.hasAddToSearchControl;
  }

  canSaveAndSharePatents() {
    return this.userService.isNotExternalUser() && !this.aggregate && this.hasTemporaryLinkControl;
  }

  canAddTask() {
    return this.userService.isNotExternalUser() && this.taskShowCreationButton &&
      !!this.userService.getUser()?.profile?.company_id && this.hasTeam;
  }

  canAddTag() {
    return this.userService.hasTagFeature() && !this.aggregate && this.hasAddTagControl;
  }

  canHarmonize() {
    return this.userService.isNotExternalUser() && !this.aggregate && this.hasHarmonizeControl;
  }

  canExportPatents() {
    return this.userService.isNotExternalUser() && this.hasExportControl && !this.storeService.searching;
  }

  canUseOctiAI(){
    return !this.userService.isFreeUser() && this.hasOctiAIControl;
  }

  get canSortBySimilarity(): boolean {
    return this.hasSortBySimilarity && !this.storeService.isPublications;
  }

  openExport() {
    this.matomoService.resultListExportButton();
    const modal = this.modalService.open(ExportDialogComponent, {size: 'xl'});
    modal.componentInstance.searchService = this.searchService;
    modal.componentInstance.storeService = this.storeService;
    modal.componentInstance.patentListScope = this.patentListScope;
    const dismissed$ = modal.dismissed.subscribe({
      next: () => {
        modal.componentInstance.restoreChartsView();
      }
    });
    this.subscriptions.add(dismissed$);
    const closed$ = modal.closed.subscribe({
      next: () => {
        modal.componentInstance.restoreChartsView();
      }
    });
    this.subscriptions.add(closed$);
    if (this.exportAdditionalParams) {
      modal.componentInstance.additionalParams = this.exportAdditionalParams;
    }
  }

  onFilterList(event: Event) {
    this.clearSuccessMessages();
    this.showFilterListDialog();
    (event.target as HTMLElement).blur();
  }

  onSortBySimilarity(event: Event) {
    this.clearSuccessMessages();
    this.showSortBySimilarityDialog();
    (event.target as HTMLElement).blur();
  }

  onCreateTaskButtonClicked(event: MouseEvent) {
    if (!this.hasSelectedPatents()) return;
    if (this.storeService.selectedPublications.length > this.storeService.pageSize) {
      this.toastService.show({
        body: `Limit reached. You can request ratings for up to ${this.storeService.pageSize} patents at once. If you need more, you can always create a new request.`,
        type: ToastTypeEnum.WARNING
      });
      return;
    }

    this.matomoService.taskManagementAddTaskButton();
    this.clearSuccessMessages();
    this.showTaskCreationForm(this.emptyTask);
    (event.target as HTMLElement).blur();
  }

  hasSelectedPatents(): boolean {
    return this.storeService.selectedPatentIds.length > 0;
  }

  showFilterListDialog() {
    const modal = this.modalService.open(FilterListDialogComponent, {
      size: 'xl',
      ariaLabelledBy: 'quick-filter-title',
      windowClass: 'filter-results-dialog',
      backdrop: 'static'
    });

    if (this.filterTitle) {
      modal.componentInstance.filterTitle = this.filterTitle;
    }

    modal.componentInstance.storeService = this.storeService;

    modal.result.then((filterData) => {
      this.storeService.clearPatentSelectionStoredData();
      if (filterData) {
        const logActivity$ = this.userService.logActivity('SEARCH_WITHIN_LIST')
          .pipe(
            catchError(() => of(null)),
            finalize(() => this.filterListEvent.emit(filterData))
          )
          .subscribe();
        this.subscriptions.add(logActivity$);
      } else {
        this.filterListEvent.emit(filterData);
      }
    }, reason => {
    });
  }

  onAddPatent(): void {
    this.addPatent.emit();
  }

  onMonitor(): void {
    this.monitor.emit();
  }

  onLandscape(): void {
    this.landscape.emit();
  }

  private getColumnsToShowByViewMode(viewMode: ViewModeTypeEnum): string[] {
    const storeName = this.getStoreServiceName();
    const columnsToShow = this.userService.getUISetting(this.columnsToShowKey, null);
    return columnsToShow && columnsToShow[storeName] && columnsToShow[storeName][viewMode] ? columnsToShow[storeName][viewMode] : this.storeService.isCombinedMode ?this.defaultSelectedColumnsToShow: null;
  }

  private getStoreServiceName(): string {
    return this.storeService.getUnderscoredStoreName();
  }

  private clearSuccessMessages() {
    this.collectionsStoreService.setSaveToCollectionSuccess(null);
    this.taskSaved.emit(null);
  }

  private showSaveToCollection() {
    this.clearSuccessMessages();
    const modal = this.modalService.open(AddToCollectionComponent, {size: 'lg'});
    modal.componentInstance.searchService = this.searchService;
    modal.componentInstance.storeService = this.storeService;
    modal.componentInstance.patentListScope = this.patentListScope;
  }

  getShareButtonTooltip() : string{
    if(this.storeService.isCollectionStore()){
      if (!this.canShareCollection()) {
        return 'You cannot share this collection'
      }
      return 'Share the collection';
    } else {
      return 'Share selected patents';
    }
  }

  canShareCollection() {
      return this.resource?.permissions?.includes('share');
  }

  isCollectionSharedViaFolder(){
    return this.resource && this.searchService instanceof CollectionService && this.searchService.isCollectionSharedViaFolder(this.resource);
  }

  private showSharePatentsDialog(stopSharing: boolean) {
    if(this.storeService.isCollectionStore()) {
      if (!this.canShareCollection()) {
        return;
      }
    }
    const modal = this.modalService.open(ShareDialogComponent, {size: 'lg'});
    modal.componentInstance.resourceId = this.saveSearchResourceId;
    modal.componentInstance.resourceType = CollaborationResourceTypeEnum.COLLECTION;
    modal.componentInstance.searchService = this.searchService;
    modal.componentInstance.storeService = this.storeService;
    modal.componentInstance.stopSharing = stopSharing;
    modal.componentInstance.patentListScope = this.patentListScope;

    if (this.saveSearchTextInput) {
      modal.componentInstance.textInput = this.saveSearchTextInput;
    }

    if (this.saveSearchHeadline) {
      modal.componentInstance.headline = this.saveSearchHeadline;
    }

    if (this.saveSearchHideAgreementCheck) {
      modal.componentInstance.hideAgreementCheck = this.saveSearchHideAgreementCheck;
    }
    if (this.resource) {
      modal.componentInstance.resource = this.resource;
      modal.componentInstance.permission = this.permission;
      modal.componentInstance.canSwitchShareType = this.resource.user_id === this.userService.getUser()?.profile?.id;
    } else {
      modal.componentInstance.createNewCollection = true;
    }

    if (this.saveSearchNameReadOnly) {
      modal.componentInstance.nameReadOnly = this.saveSearchNameReadOnly;
    }

    modal.result.then((data: ShareDialogResult) => {
      this.sharePatentsEvent.emit(data);
    }, reason => {
      this.closeShareEvent.emit(reason);
    });
  }

  private showSortBySimilarityDialog() {
    const modal = this.modalService.open(SortBySimilarityComponent, {size: 'xl'});
    modal.componentInstance.saveFromValue = this.sortBySimilarityValue;
    modal.componentInstance.storeService = this.storeService;

    modal.result.then((data) => {
      this.sortBySimilarity.emit(data);

      if (data) {
        this.storeService.patentTableSort = {
          field: 'similarity_index',
          order: 'desc'
        } as SortParams;
      } else {
        this.storeService.patentTableSort = {} as SortParams;
      }
    }, reason => {
    });
  }

  private showTaskCreationForm(task: TaskModel) {
    const modal = this.modalService.open(TaskFormComponent, {size: 'lg', keyboard: false, backdrop: 'static'});
    modal.componentInstance.task = task;
    modal.componentInstance.isDialog = true;
    modal.componentInstance.resourceId = this.taskResourceId;
    modal.componentInstance.resourceType = this.taskResourceType;
    modal.componentInstance.documentIds = this.storeService.selectedPatentIds;
    modal.componentInstance.isPublications = this.storeService.isPublications;
    const taskSaved$ = modal.componentInstance.taskSaved.subscribe({
      next: (val) => {
        this.taskSaved.emit(val);
        modal.close();
      }
    });
    this.subscriptions.add(taskSaved$);
  }

  private setSelectedColumnsToShowByViewMode(selectedColumns: string[]) {
    const selectByDefault = selectedColumns === undefined || selectedColumns === null;
    this.selectedColumnsToShow = this.columnsToShow.filter(val => {
      return selectByDefault ? val.default : selectedColumns.includes(val.property);
    }).map(item => item);

    this.storeService.selectedColumnsToShow = this.selectedColumnsToShow;
  }

  private addSimilarityColumnToShow(val: SortBySimilarityParam) {
    const colIndex = this.columnsToShow.findIndex(x => x.property === "similarity_index");
    const selectedColIndex = this.selectedColumnsToShow.findIndex(x => x.property === "similarity_index");

    const similarityIndexCol = {
      property: 'similarity_index',
      label: 'Similarity Index',
      temporary: true,
      default: true
    };

    if (val) {
      if (colIndex === -1) {
        this.columnsToShow.push(similarityIndexCol);
      }

      if (selectedColIndex === -1) {
        this.selectedColumnsToShow.push(similarityIndexCol);
        this.storeService.selectedColumnsToShow = this.selectedColumnsToShow;
      }
    } else {
      if (colIndex > -1) {
        const foundCol = this.columnsToShow[colIndex];
        if (foundCol.temporary) {
          this.columnsToShow.splice(colIndex, 1);
          if (selectedColIndex > -1) {
            this.selectedColumnsToShow.splice(selectedColIndex, 1);
          }
        }
      }
    }
  }

  closeRatingPopper(event: ShowRatingsEventParams){
    this.ratingPopper?.hide();

    if (event?.refresh) {
      this.ratingService.reloadRatingColumn = true;
    }
  }

  onRatingButtonClicked(event: any) {
    if (this.ratingPopper.isOpen || !this.hasSelectedPatents()) {
      return;
    }
    this.closeAddTagPopper();

    if (this.storeService.selectedPublications.length > this.storeService.pageSize) {
      this.toastService.show({
        header: 'Limit reached',
        body: `You can request ratings for up to the patents visible on your page (25, 50, or 100). If you need more, you can adjust the page size or create multiple requests.`,
        type: ToastTypeEnum.WARNING
      });
    } else {
      this.ratingPopper.show(event.target as HTMLElement);
    }
  }

  onOctiAIButtonClicked(event: any) {
    this.closeRatingPopper(null);
    this.closeAddTagPopper();

    if (this.octiAIPopper.isOpen) {
      this.closeOctiPopper();
    } else {
      this.octiAIPopper.show(document.querySelector(".octi-ai-bottom-wrapper") as HTMLElement);
    }
  }

  onAddTagButtonClicked(event: MouseEvent) {
    this.closeRatingPopper(null);
    this.closeOctiPopper();

    if (this.addTagPopper.isOpen) {
      this.closeOctiPopper();
    } else {
      this.addTagPopper.show(event.target as HTMLElement);
    }
  }

  closeOctiPopper() {
    this.octiAIPopper?.hide();
  }

  closeAddTagPopper() {
    this.addTagPopper?.hide();
  }

  private calculateCollapsedItems() {
    const displayedContainerEle = document.getElementById('displayed-control-bar-items');
    const temporaryItemsContainerEle = document.getElementById('temporary-control-bar-items');

    if (!displayedContainerEle || !temporaryItemsContainerEle) {
      return;
    }

    const visibleWidth = displayedContainerEle.parentElement.clientWidth;
    const displayedItems = displayedContainerEle.querySelectorAll('.item-bar');
    const temporaryItems = temporaryItemsContainerEle.querySelectorAll('.item-bar');

    const collapsedItems = [];

    for (let i = temporaryItems.length - 1; i >= 0; i--) {
      if (visibleWidth >= temporaryItemsContainerEle.clientWidth) {
        break;
      }

      const ele = temporaryItems[i] as HTMLElement;
      ele.classList.add('d-none');

      const itemType = ele.dataset.itemType as ControlBarItemEnum;
      collapsedItems.unshift(itemType);
    }

    displayedItems.forEach((ele: HTMLElement) => {
      const itemType = ele.dataset.itemType as ControlBarItemEnum;
      if (collapsedItems.includes(itemType)) {
        ele.classList.add('d-none');
      } else {
        ele.classList.remove('d-none');
      }
    });
    this.collapsedControlBarItems = collapsedItems;

    temporaryItems.forEach(item => {
      item.classList.remove('d-none');
    });
  }
}
