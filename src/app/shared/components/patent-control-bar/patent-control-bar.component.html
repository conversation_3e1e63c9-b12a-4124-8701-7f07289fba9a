<div class="similar-search-top d-flex justify-content-start align-items-center tools-bar mw-100">

  <div id="displayed-control-bar-items"
       class="d-flex justify-content-start align-items-center gap-spacing-xxx-s mw-100 overflow-hidden">
    <ng-container [ngTemplateOutlet]="controlBarItemsTemplate"></ng-container>
  </div>

  <div id="temporary-control-bar-items"
       class="d-flex justify-content-start align-items-center invisible position-absolute gap-spacing-xxx-s" style="width: max-content;">
    <ng-container [ngTemplateOutlet]="controlBarItemsTemplate"></ng-container>
  </div>
</div>

<ng-template #controlBarItemsTemplate>
  <div class="group group-separator-right p-r-spacing-xxx-s"
       *ngIf="canAddNumbersToSearch() || canAddTask() || hasAddPatent || canAddTag()  || canUseOctiAI()">
    <div class="d-flex justify-content-start align-items-center gap-spacing-xxx-s">
      <a href="javascript:void(0)" *ngIf="canAddNumbersToSearch()" (click)="enableToAdd() && addNumbersToSearch()"
         class="item-bar button-main-primary button-small content-label-small" [attr.data-item-type]="controlBarItemEnum.REFINE_SEARCH"
         [class.disabled]="!enableToAdd()"
         ngbTooltip="Refine your search with the selected results" container="body">
        <i class="fa-regular fa-magnifying-glass-waveform fa-fw"></i>
        <span class="text-nowrap">Refine search</span>
      </a>

      <a *ngIf="hasAddPatent" (click)="onAddPatent()" href="javascript:void(0)"
         [ngbTooltip]="(isPatentSelected ? 'Remove' : 'Add') + ' patents from the list'" container="body"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.REMOVE_ADD_PATENTS">
        <i class="fa-regular fa-fw"
           [class.fa-minus]="isPatentSelected"
           [class.fa-plus]="!isPatentSelected"></i>
        <span class="text-nowrap">{{ isPatentSelected ? 'Remove patents' : 'Add patents' }}</span>
      </a>

      <a href="javascript:void(0)" [ngbTooltip]="octiAITooltip" *ngIf="canUseOctiAI()"
         (click)="onOctiAIButtonClicked($event)"
         class="item-bar icon-octi-ai button-main-tertiary-grey button-small content-label-small content-color-active"
         [attr.data-item-type]="controlBarItemEnum.OCTI_AI">
        <i class="fa-regular fa-fw"></i>
        <span class="text-nowrap">Octi AI</span>
      </a>

      <span
        [ngbTooltip]="!hasSelectedPatents() ? 'Please select at least one patent in order to request ratings' : null"
        tooltipClass="white-tooltip"
        container="body">
        <a href="javascript:void(0)" *ngIf="canAddTask()" (click)="onRatingButtonClicked($event)"
           class="item-bar button-main-tertiary-grey button-small content-label-small"
           [attr.data-item-type]="controlBarItemEnum.REQUEST_RATINGS"
           [class.disabled]="!hasSelectedPatents()">
          <i class="fa-regular fa-circle-star fa-fw"></i>
          <span class="text-nowrap">Request ratings</span>
        </a>
      </span>

      <span *ngIf="canAddTag()" tooltipClass="white-tooltip" container="body"
            [ngbTooltip]="!hasSelectedPatents() ? 'Please select at least one patent in order to assign tags' : null">
        <a href="javascript:void(0)" (click)="onAddTagButtonClicked($event)"
           class="item-bar icon-add-tag button-main-tertiary-grey button-small content-label-small"
           [class.disabled]="!hasSelectedPatents()"
           [attr.data-item-type]="controlBarItemEnum.ADD_TAG">
          <i class="fa-regular fa-tags fa-fw"></i>
          <span class="text-nowrap">Add tags</span>
        </a>
      </span>
    </div>
  </div>

  <div
    *ngIf="hasFilterListControl || canSortBySimilarity || hasShowColumnControl || canHarmonize() || hasMonitor || hasLandscape"
    class="group group-separator-right p-r-spacing-xxx-s">
    <div class="d-flex justify-content-start align-items-center gap-spacing-xxx-s">
      <app-col-selector *ngIf="hasShowColumnControl"
                        ngbTooltip="Hide/show columns in the patents list" container="body"
                        [columns]="columnsToShow"
                        (selectColumns)="selectedColumnsToShowEvent($event)"
                        [selected]="selectedColumnsToShow"
                        [dataItemType]="controlBarItemEnum.CUSTOMIZE_COLUMNS"
                        dropdownToggleCss="button-main-tertiary-grey button-small content-label-small">
      </app-col-selector>

      <a href="javascript:void(0)" ngbTooltip="Group applicants under custom aliases" *ngIf="canHarmonize()"
         (click)="onHarmonize()" container="body"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.GROUP_APPLICANTS">
        <i class="fa-regular fa-layer-group fa-fw"></i>
        <span class="text-nowrap">Group applicants</span>
      </a>

      <a *ngIf="canSortBySimilarity" href="javascript:void(0)" ngbTooltip="Reorder patents based on semantic similarity"
         (click)="onSortBySimilarity($event)" container="body"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.SORT_BY_SIMILARITY"
         [ngClass]="{'btn btn-primary': !!sortBySimilarityValue, 'ms-3': isFilteredList && !!sortBySimilarityValue}">
        <i class="fa-regular fa-arrow-down-arrow-up fa-fw"></i>
        <span class="text-nowrap">Sort by similarity</span>
      </a>

      <a *ngIf="hasFilterListControl" href="javascript:void(0)" ngbTooltip="Filter patents list" container="body"
         (click)="onFilterList($event)"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.FILTER"
         [ngClass]="{'btn btn-primary': isFilteredList}">
        <i class="fa-regular fa-bars-filter fa-fw"></i>
        <span>Filter</span>
      </a>

      <a href="javascript:void(0)" ngbTooltip="Analyze patents with Landscape" container="body"
         *ngIf="hasLandscape" (click)="onLandscape()"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.LANDSCAPE">
        <i class="fa-regular fa-folder-grid fa-fw"></i>
        <span class="text-nowrap">Landscape</span>
      </a>

      <a href="javascript:void(0)" ngbTooltip="Create a Monitor profile using this collection" container="body"
         *ngIf="hasMonitor" (click)="onMonitor()"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.MONITOR">
        <i class="fa-regular fa-bullseye-arrow fa-fw"></i>
        <span class="text-nowrap">Monitor</span>
      </a>
    </div>
  </div>

  <div class="group" *ngIf="canSaveToCollection() || canExportPatents() || canSaveAndSharePatents()">
    <div class="d-flex justify-content-start align-items-center gap-spacing-xxx-s">
      <a href="javascript:void(0)" ngbTooltip="Save selected patents in your patent collection" container="body"
         *ngIf="canSaveToCollection()" (click)="onSaveToCollectionClicked($event)"
         class="item-bar save-list-item button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.SAVE">
        <i class="fa-regular fa-floppy-disk fa-fw"></i>
        <span class="text-nowrap">Save</span>
      </a>

      <a href="javascript:void(0)" ngbTooltip="Export selected patents" container="body"
         *ngIf="canExportPatents()" (click)="openExport()"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.EXPORT">
        <i class="fa-regular fa-download fa-fw"></i>
        <span class="text-nowrap">Export</span>
      </a>

      <a href="javascript:void(0)" [ngbTooltip]="shareButtonTooltip" container="body" *ngIf="canSaveAndSharePatents()"
         (click)="openSharePatents($event, isShared)"
         class="item-bar button-main-tertiary-grey button-small content-label-small"
         [attr.data-item-type]="controlBarItemEnum.SHARE">
        <i class="fa-regular fa-share-nodes fa-fw"></i>
        <span class="text-nowrap">{{ isShared ? 'Shared' : 'Share' }}</span>
      </a>
    </div>
  </div>

  <button *ngIf="collapsedControlBarItems?.length > 0 || alwaysDisplay" id="collapsed-items-button"
          [ngbPopover]="popoverCollapsedItemsTemplate" [autoClose]="true"
          popoverClass="context-menu-popper" container="body" placement="auto"
          class="figma-dropdown-btn button-main-secondary-grey button-small content-label-small content-color-tertiary border-0">
    <i class="fa-regular fa-ellipsis-vertical"></i>
  </button>
</ng-template>

<ng-template #popoverCollapsedItemsTemplate>
  <ng-container *ngFor="let item of collapsedControlBarItems">
    @switch (item) {
      @case (controlBarItemEnum.REFINE_SEARCH) {
        <a *ngIf="canAddNumbersToSearch()" (click)="enableToAdd() && addNumbersToSearch()"
           [ngClass]="{'disabled': !enableToAdd()}"
           ngbTooltip="Refine your search with the selected results" container="body" placement="left"
           class="figma-dropdown-item figma-dropdown-item-hover content-label-small content-color-primary d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-magnifying-glass-waveform fa-fw"></i>
          <span>Refine search</span>
        </a>
      }
      @case (controlBarItemEnum.REMOVE_ADD_PATENTS) {
        <a *ngIf="hasAddPatent" (click)="onAddPatent()"
           [ngbTooltip]="(isPatentSelected ? 'Remove' : 'Add') + ' patents from the list'" container="body" placement="left"
           class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-fw"
             [class.fa-minus]="isPatentSelected"
             [class.fa-plus]="!isPatentSelected"></i>
          <span>{{ isPatentSelected ? 'Remove patents' : 'Add patents' }}</span>
        </a>
      }
      @case (controlBarItemEnum.OCTI_AI) {
        <div *ngIf="canUseOctiAI()"
             (click)="onOctiAIButtonClicked($event)"
             [ngbTooltip]="octiAITooltip" container="body" placement="left"
             class="figma-dropdown-item figma-dropdown-item-hover content-label-small content-color-active icon-octi-ai d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-fw"></i>
          <span>Octi AI</span>
        </div>
      }
      @case (controlBarItemEnum.REQUEST_RATINGS) {
        <div *ngIf="canAddTask()" (click)="onRatingButtonClicked($event)"
          [ngbTooltip]="!hasSelectedPatents() ? 'Please select at least one patent in order to request ratings' : null"
          tooltipClass="white-tooltip" container="body" placement="left"
          class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s"
          [class.disabled]="!hasSelectedPatents()">
          <i class="fa-regular fa-circle-star fa-fw"></i>
          <span>Request ratings</span>
        </div>
      }
      @case (controlBarItemEnum.ADD_TAG) {
        <div *ngIf="canAddTag()" (click)="onAddTagButtonClicked($event)"
              tooltipClass="white-tooltip" container="body" placement="left"
              [ngbTooltip]="!hasSelectedPatents() ? 'Please select at least one patent in order to assign tags' : null"
              class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s"
              [class.disabled]="!hasSelectedPatents()">
          <i class="fa-regular fa-tags fa-fw"></i>
          <span>Add tag</span>
        </div>
      }
      @case (controlBarItemEnum.CUSTOMIZE_COLUMNS) {
        <app-col-selector *ngIf="hasShowColumnControl"
                          #colSelector
                          [columns]="columnsToShow"
                          (selectColumns)="selectedColumnsToShowEvent($event)"
                          [selected]="selectedColumnsToShow"
                          ngbTooltip="Hide/show columns in the patents list" container="body" placement="left"
                          dropdownToggleCss="content-label-small d-flex align-items-center gap-spacing-xx-s"
                          class="figma-dropdown-item figma-dropdown-item-hover d-flex align-items-center content-label-small"
                          (click)="colSelector.open(collapsedItemsButton)">
        </app-col-selector>
      }
      @case (controlBarItemEnum.GROUP_APPLICANTS) {
        <a *ngIf="canHarmonize()" (click)="onHarmonize()"
           ngbTooltip="Group applicants under custom aliases" container="body" placement="left"
           class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-layer-group fa-fw"></i>
          <span>Group applicants</span>
        </a>
      }
      @case (controlBarItemEnum.SORT_BY_SIMILARITY) {
        <a *ngIf="canSortBySimilarity" (click)="onSortBySimilarity($event)"
           ngbTooltip="Reorder patents based on semantic similarity" container="body" placement="left"
           class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s"
           [ngClass]="{'active': !!sortBySimilarityValue}">
          <i class="fa-regular fa-arrow-down-arrow-up fa-fw"></i>
          <span>Sort by similarity</span>
        </a>
      }
      @case (controlBarItemEnum.FILTER) {
        <a *ngIf="hasFilterListControl" (click)="onFilterList($event)"
           ngbTooltip="Filter patents list" container="body" placement="left"
           class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s"
           [ngClass]="{'active': isFilteredList}">
          <i class="fa-regular fa-bars-filter fa-fw"></i>
          <span>Filter</span>
        </a>
      }
      @case (controlBarItemEnum.LANDSCAPE) {
        <a *ngIf="hasLandscape" (click)="onLandscape()"
           ngbTooltip="Analyze patents with Landscape" container="body" placement="left"
           class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-folder-grid fa-fw"></i>
          <span>Landscape</span>
        </a>
      }
      @case (controlBarItemEnum.MONITOR) {
        <a *ngIf="hasMonitor" (click)="onMonitor()"
           ngbTooltip="Create a Monitor profile using this collection" container="body" placement="left"
           class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s"
           [attr.data-item-type]="controlBarItemEnum.MONITOR">
          <i class="fa-regular fa-bullseye-arrow fa-fw"></i>
          <span>Monitor</span>
        </a>
      }
      @case (controlBarItemEnum.SAVE) {
        <a
          *ngIf="canSaveToCollection()" (click)="onSaveToCollectionClicked($event)"
          ngbTooltip="Save selected patents in your patent collection" container="body" placement="left"
          class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-floppy-disk fa-fw"></i>
          <span>Save</span>
        </a>
      }
      @case (controlBarItemEnum.EXPORT) {
        <a *ngIf="canExportPatents()" (click)="openExport()"
             ngbTooltip="Export selected patents" container="body" placement="left"
             class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-download fa-fw"></i>
          <span>Export</span>
        </a>
      }
      @case (controlBarItemEnum.SHARE) {
        <a *ngIf="canSaveAndSharePatents()" (click)="openSharePatents($event, isShared)"
             [ngbTooltip]="shareButtonTooltip" container="body" placement="left"
             class="figma-dropdown-item figma-dropdown-item-hover content-label-small d-flex align-items-center gap-spacing-xx-s">
          <i class="fa-regular fa-share-nodes fa-fw"></i>
          <span>{{ isShared ? 'Shared' : 'Share' }}</span>
        </a>
      }
    }
  </ng-container>
</ng-template>

<ng-template #shareButtonTooltip>
  <span [innerHTML]="getShareButtonTooltip()"></span>
</ng-template>

<app-popper #ratingPopper
            placement="bottom" [showArrow]="false" [resizePopperWidth]="false"
            customClass="button-control-bar-popper p-spacing-none"
            [allowedClickClasses]="['popper-inner', 'ngb-dp-body']">
  <div class="button-rating-form-popper">
    <app-patent-rating-form *ngIf="canAddTask() && ratingPopper.isOpen" [storeService]="storeService"
                            [showCloseButton]="false"
                            [task]="emptyTask"
                            [resourceId]="taskResourceId"
                            [resourceType]="taskResourceType"
                            (hideRatingFormEvent)="closeRatingPopper($event)">
    </app-patent-rating-form>
  </div>
</app-popper>

<div class="octi-ai-bottom-wrapper" *ngIf="canUseOctiAI()" [ngClass]="{'ip-lounge-user': isIpLounge}">
  <ng-template #octiAILauncherTooltip>
    <div class="d-flex">
      <div class="content-color-active m-r-spacing-xxx-s">
        <i class="fa-regular fa-sparkles"></i>
      </div>
      <div class="d-flex flex-column text-start">
        <div class="content-heading-h6 content-color-secondary">Hi I'm Octi!</div>
        <div class="content-body-small content-color-secondary">If you need help going through these documents, I'm here to support you.</div>
      </div>
    </div>
  </ng-template>
  <i class="octi-ai-bottom-launcher" *ngIf="!octiAIPopper.isOpen" tooltipClass="tooltip-sm" [ngbTooltip]="octiAILauncherTooltip" (click)="onOctiAIButtonClicked($event)"></i>
</div>

<app-popper #octiAIPopper placement="top-end" [showArrow]="false" [resizePopperWidth]="false"
  customClass="octi-ai-popper p-spacing-none super-z-index" [allowedClickClasses]="['popper-inner', 'ngb-dp-body', 'publication-table']">
  <app-octi-panel *ngIf="octiAIPopper.isOpen && canUseOctiAI()" [storeService]="storeService"
    [isAskByPatent]="false" [showHeader]="true" [chatID]="searchHash" wrapperClass="scrollbar-2024"
    [range]="octiListChatRange" [documents]="getDocuments()"
    (closePanel)="closeOctiPopper()">
  </app-octi-panel>
</app-popper>

<app-popper #addTagPopper placement="bottom" [showArrow]="false" [resizePopperWidth]="false"
            customClass="p-spacing-sm tags-select-patent-control-bar super-z-index"
            [allowedClickClasses]="['tags-select-patent-control-bar']">
  <app-tags-select *ngIf="addTagPopper.isOpen && canAddTag()" [canAddTag]="true"
                   [storeService]="storeService" [multipleTaggingDocumentIds]="storeService.selectedPatentIds"
                   (tagsSelectAddedTag)="addTagPopper.hide()"
                   (tagsSelectCanceled)="addTagPopper.hide()">
  </app-tags-select>
</app-popper>

<ng-template #octiAITooltip>
  <div class="d-flex">
    <div class="d-flex flex-column text-start">
      <div class="content-heading-h6 content-color-secondary">Hi, I'm Octi!</div>
      <div class="content-body-small content-color-secondary">If you need help going through these documents, I'm here
        to support you.
      </div>
    </div>
  </div>
</ng-template>
