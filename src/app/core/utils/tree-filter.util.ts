import { FilterHighlightsOptionEnum, PatentCommentsFilterEnum, TreeFilterItemType, TreeFilterOption } from '@patent/types';
import { TeamUser } from '@core/models';
import { DocumentAnnotation } from '@core/services';
import { UserTitlePipe } from '@core/pipes';

export class TreeFilterUtil {
  static findSelectedFilterOptions(options: TreeFilterOption[]): TreeFilterOption[] {
    const findSelectedNode = (node: TreeFilterOption): TreeFilterOption[] => {
      const selectedOptions = [];
      if (node.selected) {
        selectedOptions.push(node);
      }
      if (!node.children) {
        return selectedOptions;
      }
      for (const child of node.children) {
        selectedOptions.push(...findSelectedNode(child));
      }
      return selectedOptions;
    }

    const selectedOptions = [];
    for (let o of options) {
      selectedOptions.push(...findSelectedNode(o));
    }
    return selectedOptions;
  }

  static findFilterOption(op: TreeFilterOption, options: TreeFilterOption[]): TreeFilterOption {
    const findNode = (currentNode: TreeFilterOption): TreeFilterOption => {
      if (this.isTreeFilterOptionEqual(op, currentNode)) {
        return currentNode;
      }
      if (!currentNode.children) {
        return null;
      }
      for (let child of currentNode.children) {
        const found = findNode(child);
        if (found) {
          return found;
        }
      }
    }

    for (let o of options) {
      const found = findNode(o);
      if (found) {
        return found;
      }
    }
    return null;
  }

  static findParentFilterOption(op: TreeFilterOption, options: TreeFilterOption[]): TreeFilterOption {
    const findNode = (parentNode: TreeFilterOption, currentNode: TreeFilterOption): TreeFilterOption => {
      if (this.isTreeFilterOptionEqual(op, currentNode)) {
        return parentNode;
      }
      if (!currentNode.children) {
        return null;
      }
      for (let child of currentNode.children) {
        const found = findNode(currentNode, child);
        if (found) {
          return found;
        }
      }
    }

    for (let o of options) {
      const found = findNode(null, o);
      if (found) {
        return found;
      }
    }
    return null;
  }

  static selectFilterOption(option: TreeFilterOption, options: TreeFilterOption[], defaultOption: TreeFilterOption) {
    const selectedOptions = this.findSelectedFilterOptions(options);
    if (option.isToggle) {
      for (let op of selectedOptions) {
        op.selected = false;
      }
      option.selected = true;
    } else {
      for (let op of selectedOptions) {
        if (op.depth != option.depth) {
          op.selected = false;
        }
      }
      option.selected = !option.selected;
    }

    if (this.findSelectedFilterOptions(options).length === 0) {
      defaultOption.selected = true;
    }
  }

  static hasSelectedFilterChildren(option: TreeFilterOption, options: TreeFilterOption[]) {
    const findSelectedChild = (currentNode: TreeFilterOption): boolean => {
      if (currentNode.selected) {
        return true;
      }
      if (!currentNode.children) {
        return false;
      }
      for (let child of currentNode.children) {
        if (findSelectedChild(child)) {
          return true;
        }
      }
      return false;
    }

    const foundOption = this.findFilterOption(option, options);

    if (!foundOption || !foundOption.children?.length) {
      return false;
    }

    for (let o of foundOption.children) {
      if (findSelectedChild(o)) {
        return true;
      }
    }
    return false;
  }

  static isTreeFilterOptionEqual(a: TreeFilterOption, b: TreeFilterOption): boolean {
    if (!a || !b) {
      return false;
    }
    return a.item === b.item && a.itemType === b.itemType && a.depth === b.depth;
  }

  static isFilterByDefault(options: TreeFilterOption[], defaultOption: TreeFilterOption) {
    const selectedOptions = this.findSelectedFilterOptions(options);
    if (selectedOptions.length != 1) {
      return false;
    }
    return this.isTreeFilterOptionEqual(selectedOptions[0], defaultOption);
  }

  static isFilterByTeamUsers(options: TreeFilterOption[]) {
    const selectedOptions = this.findSelectedFilterOptions(options);
    return selectedOptions.some(o => o.itemType === TreeFilterItemType.TEAM_USER);
  }

  static parentOfSelectedFilterOption(options: TreeFilterOption[]): TreeFilterOption {
    const selectedOptions = this.findSelectedFilterOptions(options);
    const found = selectedOptions.find(o => o.selected && o.depth === 0);
    if (found) {
      return found;
    }

    const lowestDepthOption = selectedOptions.sort((a, b) => a.depth - b.depth)[0];
    return this.findParentFilterOption(lowestDepthOption, options);
  }

  static updateCreatedByFilterOption(
    options: TreeFilterOption[], createdByEnum: PatentCommentsFilterEnum | FilterHighlightsOptionEnum, userId: number,
    items: DocumentAnnotation[], userTitlePipe: UserTitlePipe, profileAsTeamUser: TeamUser, defaultOption: TreeFilterOption
  ) {
    const selectedUserIds = this.findSelectedFilterOptions(options)
      .filter(o => o.itemType === TreeFilterItemType.TEAM_USER)
      .map(o => (o.item as TeamUser).id);

    const createdByOption = options.find((p) => p.item === createdByEnum);

    const getUsers = (comment: DocumentAnnotation): TeamUser[] => {
      const users = new Set<TeamUser>();
      if (comment.user_id !== userId) {
        users.add(comment.user);
      }
      (comment.replies || []).forEach(r => {
        if (r.user_id !== userId) {
          users.add(r.user);
        }
      });
      return [...users];
    };
    const users = [...new Set(items.map(getUsers).flat())];
    const userOptions = [];
    for (let u of users) {
      userOptions.push({
        item: u,
        itemType: TreeFilterItemType.TEAM_USER,
        isCheckbox: true,
        selected: selectedUserIds.includes(u.id)
      } as TreeFilterOption);
    }
    userOptions.sort((a, b) => userTitlePipe.transform(a.item as TeamUser).localeCompare(userTitlePipe.transform(b.item as TeamUser)));
    const meOption = {
      item: profileAsTeamUser,
      itemType: TreeFilterItemType.TEAM_USER,
      isCheckbox: true,
      selected: selectedUserIds.includes(userId)
    } as TreeFilterOption;
    createdByOption.children = [meOption, ...userOptions];

    if (!this.findSelectedFilterOptions(options).length) {
      defaultOption.selected = true;
    }
  }
}
