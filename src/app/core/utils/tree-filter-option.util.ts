export class UrlUtil {
  static queryParamsToUrlString(queryParams: object): string {
    const str = [];
    for (const p in queryParams) {
      if (queryParams.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(queryParams[p]));
      }
    }
    return ((str.length > 0 ? '?' : '') + str.join('&'));
  }

  static splitQueryParamValue<T>(value: string, mappingFunc: (val: string) => T, separator: string = ','): T[] {
    return (value || '').split(separator)
      .map(v => v.trim())
      .filter(v => v.length > 0)
      .map(v => {
        if (mappingFunc) {
          return mappingFunc(v);
        }
        return v;
      }) as T[];
  }
}
