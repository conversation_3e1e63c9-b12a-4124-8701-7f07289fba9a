import { Injectable } from '@angular/core';
import { TextHighlightService } from '../text-highlight/text-highlight.service';
import { AnnotationPosition, DocumentAnnotation, Label } from '../annotation';
import { Subject } from 'rxjs';
import {
  FilterHighlightsOptionEnum,
  InlineHighlightFilterOptionEnum,
  PatentCommentsFilterEnum,
  SortHighlightsOptionEnum,
  TreeFilterItemType,
  TreeFilterOption
} from '@patent/types';
import { TeamUser, TreeFilterUtil, UserService, UserTitlePipe } from '@core';

@Injectable({
  providedIn: 'root'
})
export class PatentViewHighlightService {

  constructor(
    private textHighlightService: TextHighlightService,
    private userService: UserService,
    private userTitlePipe: UserTitlePipe,
  ) { }

  mouseDownElementId: string;
  originalHighlights: DocumentAnnotation[] = [];
  highlightList: DocumentAnnotation[] = [];
  highlights: Label[] = [];
  selectedColor: string;
  currentSelectedContainer: { range: any; content: any; highlightNode: any; };
  removedHighlightEvent$ = new Subject<number>();
  savedHighlightEvent$ = new Subject<{savedHighlight: DocumentAnnotation, selectedElements: Element[]}>();

  currentInlineFilter: InlineHighlightFilterOptionEnum = InlineHighlightFilterOptionEnum.SHOW_ALL;

  highlightsFilterOptions: TreeFilterOption[] = [
    {
      item: FilterHighlightsOptionEnum.ALL,
      itemType: TreeFilterItemType.HIGHLIGHTS_FILTER_ENUM,
      depth: 0, selected: true, isToggle: true
    },
    {
      item: PatentCommentsFilterEnum.CREATED_BY,
      itemType: TreeFilterItemType.HIGHLIGHTS_FILTER_ENUM,
      children: [], hasChildren: true, depth: 0, selected: false, multiple: true,
    },
  ];

  highlightsSortBy = SortHighlightsOptionEnum.RECENTLY_ADDED;

  private textSelectionTagIds = ['abstract-text', 'claims-text', 'description-text'];
  private maxSelectedTextLen = 2;

  private get highlightCssSelector(): string {
    return `.${TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS}`;
  }

  get highlightsFilterBy(): TreeFilterOption[] {
    return this.findSelectedHighlightsFilterOptions();
  }

  get parentOfSelectedFilterOption(): TreeFilterOption {
    return TreeFilterUtil.parentOfSelectedFilterOption(this.highlightsFilterOptions);
  }

  get defaultSelectedHighlightsFilterOption(): TreeFilterOption {
    return this.highlightsFilterOptions.find(o => o.item === FilterHighlightsOptionEnum.ALL);
  }

  applyHighlight() {
    const tag = document.getElementById(this.mouseDownElementId);
    const {startPos, endPos} = this.getSelectedAnnotationPosition(tag);
    let highlight = document.querySelector(this.highlightCssSelector);

    let highlightsSelection = [];
    while (highlight) {
      if (!highlight.textContent) {
        highlight.remove();
      }
      this.setStyleHighlight(<HTMLElement>highlight);
      const parent = highlight.parentNode as HTMLElement;
      if (parent?.classList.contains('highlight')) {
        highlightsSelection = this.processHighlightSelection(highlight, highlightsSelection);
      } else {
        highlightsSelection.push(highlight)
      }

      highlight = document.querySelector(this.highlightCssSelector);

    }

    this.currentSelectedContainer = {range: null, content: null, highlightNode: null};
    const textContent = highlightsSelection.reduce((acc, el) => acc + el.textContent, '');
    const highlightToSave = {
      label_id: this.highlights.find(hl => hl.color === this.selectedColor).id,
      text: textContent,
      start_pos: startPos,
      end_pos: endPos
    }
    this.savedHighlightEvent$.next({savedHighlight: highlightToSave, selectedElements: highlightsSelection});
  }

  private getSelectedAnnotationPosition(tag: HTMLElement): { startPos: number, endPos: number } {
    return this.textHighlightService.getSelectedTextPosition(tag, TextHighlightService.HIGHLIGHT_TEXT_TMP_CSS_CLASS);
  }

  private setStyleHighlight(highlight: HTMLElement) {
    highlight.style.backgroundColor = this.selectedColor;
    highlight.classList.add('highlight');
    highlight.classList.remove(TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS, TextHighlightService.HIGHLIGHT_TEXT_TMP_CSS_CLASS);
  }

  private processHighlightSelection(highlight, highlightsSelection): Element[] {
    const parent = highlight.parentNode as HTMLElement;
    const textSegments = this.splitParentNodeAndAdjustHighlightTextContent(highlight);

    if (!textSegments[0] && !textSegments[1]) {
      return this.precessEmptyTextSegments(parent, highlight, highlightsSelection);
    }

    if (textSegments[0] && textSegments[1]) {
      return this.processBothTextSegments(parent, highlight, textSegments, highlightsSelection);
    }

    if (textSegments[0]) {
      return this.processPreviousTextSegment(parent, highlight, textSegments, highlightsSelection);
    }

    if (textSegments[1]) {
      return this.processNextTextSegment(parent, highlight, textSegments, highlightsSelection);
    }
  }

  private splitParentNodeAndAdjustHighlightTextContent(highlight: HTMLElement) {
    const parent = highlight.parentNode;
    const textSegments = parent.textContent.split(highlight.textContent);
    if (textSegments[0] && !textSegments[0].trim()) {
      highlight.textContent = textSegments[0] + highlight.textContent;
      textSegments[0] = '';
    }
    if (textSegments[1] && !textSegments[1].trim()) {
      highlight.textContent = highlight.textContent + textSegments[1];
      textSegments[1] = '';
    }
    return textSegments;
  }

  private precessEmptyTextSegments(parent, highlight, highlightsSelection): Element[] {
    const { previousHighlights, nextHighlights } = this.getSiblingHighlights(parent);

    parent.parentNode.insertBefore(highlight, parent.nextSibling);

    if (previousHighlights.length === 0 && nextHighlights.length === 0) {
      this.removedHighlightEvent$.next(this.getIdFromElement(parent));
      highlightsSelection.push(highlight);
      parent.remove();
      return highlightsSelection;
    }

    if (previousHighlights.length === 0) {
      highlightsSelection.push(highlight);
      highlightsSelection = this.processHighlightNextSiblings(parent, highlight, nextHighlights, highlightsSelection);
      parent.remove();
      return highlightsSelection;
    }

    const previousTextContent = previousHighlights.reduce((acc, ph) => acc + ph.textContent, '');
    this.updateAndSaveHighlight(parent, previousTextContent, previousHighlights);

    highlightsSelection.push(highlight);
    if (nextHighlights.length == 0) {
      parent.remove();
      return highlightsSelection;
    }

    highlightsSelection = this.processHighlightNextSiblings(parent, highlight, nextHighlights, highlightsSelection, previousTextContent.length);
    parent.remove();
    return highlightsSelection;
  }

  private processBothTextSegments(parent, highlight, textSegments, highlightsSelection): Element[] {
    const { previousHighlights, nextHighlights } = this.getSiblingHighlights(parent);
    const previousTextContent = textSegments[0] + previousHighlights.reduce((acc, ph) => acc + ph.textContent, '');
    const nextTextContent = nextHighlights.reduce((acc, nh) => acc + nh.textContent, '') + textSegments[1];
    const nextElement = parent.cloneNode(true);

    nextElement.textContent = nextTextContent;
    parent.parentNode.insertBefore(nextElement, parent.nextSibling);
    parent.parentNode.insertBefore(highlight, parent.nextSibling);
    parent.textContent = previousTextContent;
    nextHighlights.unshift(nextElement);

    this.updateAndSaveHighlight(parent, previousTextContent, previousHighlights);
    const savedHighlight = this.highlightList.find(hl => hl.id === this.getIdFromElement(parent));
    const startPos = savedHighlight.start_pos + previousTextContent.length + highlight.textContent.length;
    const nextHighlight = {
      label_id: savedHighlight.label_id,
      text: nextTextContent,
      start_pos: startPos,
      end_pos: startPos + nextTextContent.length
    };
    this.savedHighlightEvent$.next({savedHighlight: nextHighlight, selectedElements: nextHighlights});
    highlightsSelection.push(highlight);
    return highlightsSelection;
  }

  private processPreviousTextSegment(parent, highlight, textSegments, highlightsSelection): Element[] {
    const { previousHighlights, nextHighlights } = this.getSiblingHighlights(parent);

    parent.parentNode.insertBefore(highlight, parent.nextSibling);
    previousHighlights.push(parent);
    const previousTextContent = previousHighlights.reduce((acc, ph) => acc + ph.textContent, '');

    this.updateAndSaveHighlight(parent, previousTextContent, previousHighlights);
    highlightsSelection.push(highlight);

    if (nextHighlights.length === 0) {
      return highlightsSelection;
    }

    return this.processHighlightNextSiblings(parent, highlight, nextHighlights, highlightsSelection, previousTextContent.length);
  }

  private processNextTextSegment(parent, highlight, textSegments, highlightsSelection): Element[] {
    const { nextHighlights } = this.getSiblingHighlights(parent);
    const nextTextContent = textSegments[1] + nextHighlights.reduce((acc, nh) => acc + nh.textContent, '');

    parent.parentNode.insertBefore(highlight, parent);
    nextHighlights.unshift(parent);

    this.updateAndSaveHighlight(parent, nextTextContent, nextHighlights);
    highlightsSelection.push(highlight);
    return highlightsSelection;
  }

  processHighlightNextSiblings(parent, highlight: any, nextHighlights: Element[], highlightsSelection: Element[], lengthPrevious = 0): Element[] {
    const selectedSiblings = nextHighlights.filter(nextNode => nextNode.querySelector(this.highlightCssSelector));
    const noSelectedSiblings = nextHighlights.filter(nextNode => !nextNode.querySelector(this.highlightCssSelector));

    let selectedTextContent = '';
    let noSelectedTextContent = '';

    selectedSiblings.forEach(selectedNode => {
      let noSelectedTextNode = '';
      const selectedHighlight = selectedNode.querySelector(this.highlightCssSelector) as HTMLElement;
      selectedTextContent += selectedHighlight.textContent;
      if (selectedHighlight.textContent !== selectedNode.textContent) {
        noSelectedTextNode = selectedNode.textContent.replace(selectedTextContent, '');
        noSelectedSiblings.unshift(selectedNode);
      }
      this.setStyleHighlight(selectedHighlight);
      selectedNode.parentNode.insertBefore(selectedHighlight, selectedNode);
      if (!noSelectedTextNode) {
        selectedNode.remove();
      }
      highlightsSelection.push(selectedHighlight);
    });

    if (noSelectedSiblings.length === 0 && !noSelectedTextContent) {
      if (lengthPrevious === 0) {
        this.removedHighlightEvent$.next(this.getIdFromElement(parent));
      }
      return highlightsSelection;
    }
    noSelectedTextContent = noSelectedSiblings.reduce((acc, ss) => acc + ss.textContent, '');

    const savedHighlight = this.highlightList.find(hl => hl.id === this.getIdFromElement(parent));
    const length = lengthPrevious === 0 ? 0 : lengthPrevious;
    const startPos = savedHighlight.start_pos + length + selectedTextContent.length + highlight.textContent.length ;
    const endPos = startPos + noSelectedTextContent.length;
    let highlightToSave = {savedHighlight: {}, selectedElements: []};

    if (lengthPrevious === 0) {
      savedHighlight.text = noSelectedTextContent;
      savedHighlight.end_pos = endPos;
      highlightToSave = {savedHighlight: savedHighlight, selectedElements: noSelectedSiblings};
    } else {
      highlightToSave = {
        savedHighlight: {
                          label_id: savedHighlight.label_id,
                          text: noSelectedTextContent,
                          start_pos: startPos,
                          end_pos: endPos
                        },
        selectedElements: noSelectedSiblings
      };
    };

    this.savedHighlightEvent$.next(highlightToSave);
    return highlightsSelection;
  }

  private getSiblingHighlights(parent: HTMLElement) {
    if (!parent.dataset?.id) {
      return { previousHighlights: [], nextHighlights: [] };
    }
    const highlights =  this.getHighlightsByDataId(this.getIdFromElement(parent));
    const previousHighlights = highlights.filter(el => el.compareDocumentPosition(parent) & Node.DOCUMENT_POSITION_FOLLOWING);
    const nextHighlights = highlights.filter(el => el.compareDocumentPosition(parent) & Node.DOCUMENT_POSITION_PRECEDING);
    return { previousHighlights, nextHighlights };
  }

  private updateAndSaveHighlight(parent, text, highlights) {
    const savedHighlight = this.highlightList.find(highlight => highlight.id === this.getIdFromElement(parent));
    savedHighlight.text = text;
    savedHighlight.end_pos = savedHighlight.start_pos + text.length;
    this.savedHighlightEvent$.next({savedHighlight: savedHighlight, selectedElements: highlights});
    return savedHighlight;
  }

  getElementId(event) {
    for (const tag of event.composedPath()) {
      let tagId = (tag as Element).id;
      tagId = tagId ? tagId.toLowerCase() : tagId;

      if (this.textSelectionTagIds.includes(tagId)) {
        return tagId;
      }
    }
    return null;
  }

  private getIdFromElement(element: HTMLElement): number {
    return parseInt(element.dataset.id?.replace('hl-', ''));
  }

  clearCurrentHighlight() {
    this.textHighlightService.clearCurrentHighlight(this.currentSelectedContainer);
    this.currentSelectedContainer = {range: null, content: null, highlightNode: null};
  }

  setHighlightSelection(event: MouseEvent): {range: any, content: any, highlightNode: any}{
    if (this.mouseDownElementId) {

      const selection = this.textHighlightService.getSelection(event);
      const selectedText = selection ? selection.toString() : '';

      if (selectedText.length >= this.maxSelectedTextLen) {
        const containerElement = document.getElementById(this.mouseDownElementId);
        const range = selection.getRangeAt(0);

        if (range.commonAncestorContainer.nodeType !== Node.TEXT_NODE &&
            range.commonAncestorContainer != containerElement && !containerElement.contains(range.commonAncestorContainer)) {
          const rangeContainer = document.createRange();
          rangeContainer.selectNodeContents(containerElement.firstChild);

          let startWithinDiv1 = (range.compareBoundaryPoints(Range.START_TO_START, rangeContainer) >= 0);
          let endWithinDiv1 = (range.compareBoundaryPoints(Range.END_TO_END, rangeContainer) <= 0);
          let intersectionRange = range.cloneRange();

          if (startWithinDiv1 || endWithinDiv1) {
              if (!startWithinDiv1) {
                  intersectionRange.setStart(containerElement, 0);
              }
              if (!endWithinDiv1) {
                  intersectionRange.setEnd(containerElement, containerElement.childNodes.length);
              }
          }

          selection.removeAllRanges();
          selection.addRange(intersectionRange);
        }

        const highlightResult = this.textHighlightService.highlightSelectedText(selection, containerElement, TextHighlightService.HIGHLIGHT_TEXT_TMP_CSS_CLASS);


        if (!highlightResult.range) {
          selection.removeAllRanges();
          return null;
        }

        this.currentSelectedContainer = highlightResult;
        return highlightResult;
      }
    }
  }

  removeHighlightsBySelection(): boolean {
    let highlightToRemove = document.querySelector(this.highlightCssSelector);
    while (highlightToRemove) {
      const parent = highlightToRemove.parentNode as HTMLElement;
      const selectionText = highlightToRemove.textContent || '';
      const selectionNode = document.createTextNode(highlightToRemove.textContent);
      parent.replaceChild(selectionNode, highlightToRemove);

      if (selectionText && parent?.classList.contains('highlight')) {
        if (parent.textContent === selectionText) {
          this.removeCompleteHighlight(parent, selectionText);
        } else {
          this.removePartialHighlight(parent, selectionText, selectionNode);
        }
      }
      highlightToRemove = document.querySelector(this.highlightCssSelector);
    }

    this.currentSelectedContainer = {range: null, content: null, highlightNode: null};
    return true;
  }

  private removeCompleteHighlight(parent: HTMLElement, selectionText: string) {
    const parentText = document.createTextNode(parent.textContent);
    parent.parentNode?.replaceChild(parentText, parent);

    const { previousHighlights, nextHighlights } = this.getSiblingHighlights(parent);

    if (previousHighlights.length === 0 && nextHighlights.length === 0) {
      this.removedHighlightEvent$.next(this.getIdFromElement(parent));
      return;
    }

    const savedHighlight = this.highlightList.find(highlight => highlight.id == this.getIdFromElement(parent));
    let previousStartPos = 0;
    let previousLength = 0;
    let previousEndPos = 0;
    let previousTextContent = '';
    if (previousHighlights.length > 0) {
      previousTextContent = previousHighlights.reduce((acc, ph) => acc + ph.textContent, '');
      if (previousTextContent) {
        savedHighlight.text = previousTextContent;
        savedHighlight.end_pos = savedHighlight.start_pos + previousTextContent.length;
        previousStartPos = savedHighlight.start_pos;
        previousEndPos = savedHighlight.end_pos;
        previousLength = previousTextContent.length;
        this.savedHighlightEvent$.next({savedHighlight: savedHighlight, selectedElements: previousHighlights});
      } else {
        previousHighlights.forEach(previousNode => {
          const previousText = document.createTextNode(previousNode.textContent);
          previousNode.parentNode.replaceChild(previousText, previousNode);
        });
        if (nextHighlights.length === 0) {
          this.removedHighlightEvent$.next(this.getIdFromElement(parent));
        }
      }
    }

    if (nextHighlights.length > 0) {
      const nextSelectedHighlights = nextHighlights.filter(nextNode => nextNode.querySelector(this.highlightCssSelector) || nextNode.children.length === 0);
      const nextNoSelectedHighlights = nextHighlights.filter(nextNode => !nextNode.querySelector(this.highlightCssSelector) && nextNode.children.length > 0);

      const nextSelectedTextContent = nextSelectedHighlights.reduce((acc, sibling) => acc + sibling.textContent, '');
      if (nextSelectedHighlights.length > 0) {
        nextSelectedHighlights.forEach(nextNode => {
          const nextText = document.createTextNode(nextNode.textContent);
          nextNode.parentNode.replaceChild(nextText, nextNode);
        });
        if (previousHighlights.length === 0 && nextNoSelectedHighlights.length === 0) {
          this.removedHighlightEvent$.next(this.getIdFromElement(parent));
        }
      }

      if (nextNoSelectedHighlights.length > 0) {
        const newTextContent = nextNoSelectedHighlights.reduce((acc, sibling) => acc + sibling.textContent, '');
        if (previousHighlights.length > 0) {
          const startPos = previousEndPos + selectionText.length +  nextSelectedTextContent.length;
          const newHighlightToSave = {
            label_id: savedHighlight.label_id,
            text: newTextContent,
            start_pos: startPos,
            end_pos:  startPos + newTextContent.length
          };
          this.savedHighlightEvent$.next({savedHighlight: newHighlightToSave, selectedElements: nextNoSelectedHighlights});
        } else {
          const startPos = selectionText.length + nextSelectedTextContent.length;
          savedHighlight.start_pos = startPos;
          savedHighlight.end_pos = startPos + newTextContent.length;
          savedHighlight.text = newTextContent;
          this.savedHighlightEvent$.next({savedHighlight: savedHighlight, selectedElements: nextNoSelectedHighlights});
        }
      }

    }
  }

  private removePartialHighlight(parent: HTMLElement, selectionText: string, selectionNode: Text) {
    const textSegments = parent.textContent.split(selectionText);
    if (textSegments[0]) {
      this.processPreviousTextSegmentsRemoval(parent, selectionNode, textSegments, selectionText);
      return;
    }

    if (textSegments[1]) {
      this.processNextTextSegmentsRemoval(parent, selectionNode, textSegments[1], selectionText);
      return;
    }

    this.removedHighlightEvent$.next(this.getIdFromElement(parent));
  }

  private processPreviousTextSegmentsRemoval(parent: HTMLElement, selectionNode: Text, textSegments: string[], selectionText: string) {
    parent.textContent = textSegments[0];
    parent.parentNode?.insertBefore(selectionNode, parent.nextSibling);
    const savedHighlight = this.highlightList.find(highlight => highlight.id == this.getIdFromElement(parent));
    const siblings = this.getSiblingHighlights(parent);
    let textPrevious = '';
    siblings.previousHighlights.forEach(previousNode => {
      textPrevious += previousNode.textContent;
    });
    savedHighlight.text = textPrevious + textSegments[0];
    savedHighlight.end_pos = savedHighlight.start_pos + savedHighlight.text.length;
    this.savedHighlightEvent$.next({savedHighlight: savedHighlight, selectedElements: [parent]});

    if (textSegments[1]) {
      const newChild = document.createElement('span');
      newChild.textContent = textSegments[1];
      newChild.classList.add('highlight');

      newChild.style.backgroundColor = '#' + savedHighlight.label.color;
      parent.parentNode?.insertBefore(newChild, parent.nextSibling.nextSibling);

      const startPos = savedHighlight.start_pos + textSegments[0].length + selectionText.length;
      this.savedHighlightEvent$.next({
        savedHighlight: {label_id: savedHighlight.label_id, text: textSegments[1],
                         start_pos: startPos, end_pos: startPos + textSegments[1].length},
        selectedElements: [newChild]
      });
    }
  }

  private processNextTextSegmentsRemoval(parent: HTMLElement, selectionNode: Text, text: string, selectionText: string) {
    parent.parentNode?.insertBefore(selectionNode, parent.previousSibling.nextSibling);
    parent.textContent = text;
    const savedHighlight = this.highlightList.find(highlight => highlight.id == this.getIdFromElement(parent));
    savedHighlight.text = text;
    savedHighlight.start_pos = savedHighlight.start_pos + selectionText.length;
    savedHighlight.end_pos = savedHighlight.start_pos + text.length;
    this.savedHighlightEvent$.next({savedHighlight: savedHighlight, selectedElements: [parent]});
  }

  removeHighlightsById(highlightId: number, deleteHighlight: boolean) {
    const highlightsEle = this.getHighlightsByDataId(highlightId);
    for (let ele of highlightsEle) {
      const textNode = document.createTextNode(ele.textContent || '');
      ele.parentNode?.replaceChild(textNode, ele);
    }
    if (deleteHighlight) {
      this.removedHighlightEvent$.next(highlightId);
    }
  }

  getHighlightsByDataId(id): Element[] {
    return Array.from(document.querySelectorAll(`[data-id="hl-${id}"]`));
  }

  private getHighlightsByClassName(className: string): Element[] {
    return Array.from(document.getElementsByClassName(className));
  }

  setHighlightSavedAnnotation(highlight: DocumentAnnotation): void {
    const highlightEleId = 'hl-' + highlight.id;
    const highlightedEle = document.querySelector(`[data-id="${highlightEleId}"]`);

    if (highlightedEle) {
      return;
    }

    const field = highlight.field + '-text';
    const node: HTMLElement = document.getElementById(field);
    this.mouseDownElementId = field;

    let isTextValid = this.textHighlightService.validateText(node, highlight);

    if (!isTextValid) {
      let newPosition: AnnotationPosition = this.textHighlightService.getNewPosition(node, highlight);
      if (newPosition) {
        highlight.start_pos = newPosition.startPos;
        highlight.end_pos = newPosition.endPos;
        isTextValid = true;
      }
    }

    if (isTextValid) {
      window.getSelection().removeAllRanges();
      const position = this.textHighlightService.getSavedAnnotationPosition(node, highlight);
      const range = document.createRange();
      range.setStart(position.startNode, position.startPos);
      range.setEnd(position.endNode, position.endPos);

      const highlightNode = document.createElement('span');
      highlightNode.dataset.id = highlightEleId;

      this.selectedColor = '#' + highlight.label.color;
      this.setStyleHighlight(highlightNode);
      this.selectedColor = '';
      if (range.commonAncestorContainer.nodeType === Node.TEXT_NODE) {
        range.surroundContents(highlightNode);
      } else {
        const selection = window.getSelection();
        selection.addRange(range);

        this.textHighlightService.setHighlightChildNodes(range, highlightNode);
      }
      window.getSelection().removeAllRanges();
    } else {
      this.highlightList = this.highlightList.filter(hl => hl.id !== highlight.id);

    }
    this.mouseDownElementId = '';
  }

  getSelectedHighlightColor(): {selectedColor?: string, defaultColor?: string} {
    const selectedText = this.getHighlightsByClassName(TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS);
    let color: string;

    const allSameColor = selectedText.every(highlight => {
      const parent = highlight.parentNode as HTMLElement;
      let highlightColor = '';
      if (parent.classList.contains('highlight')) {
        const savedHighlight = this.highlightList.find(hl => hl.id === this.getIdFromElement(parent));
        highlightColor = savedHighlight?.label?.color;
      }
      if (color == undefined) {
        color = highlightColor;
      }
      return color === highlightColor;
    });
    if (!allSameColor) {
      return {selectedColor: '', defaultColor: ''};
    }
    return color ? {selectedColor: '#' + color, defaultColor: ''} : {selectedColor: '', defaultColor: this.highlights[0].color};
  }

  isThereHighlightsInNewSelection(): boolean {
    const selectedText = this.getHighlightsByClassName(TextHighlightService.HIGHLIGHT_TEXT_CSS_CLASS);
    return selectedText.some(highlight => {
      const parent = highlight.parentNode as HTMLElement;
      return parent.classList.contains('highlight');
    });
  }

  updateHighlightColor(id: number): boolean {
    const highlights = this.getHighlightsByDataId(id);
    if (highlights.length > 0) {
      highlights.forEach((highlight) => {
        const highlightElement = highlight as HTMLElement;
        highlightElement.style.backgroundColor = this.selectedColor;
      });
      return true;
    }
    return false;
  }

  filterHighlights() {
    const userId = this.userService.userId;

    this.highlightList = this.originalHighlights.filter(hl => {
      if (this.currentInlineFilter === InlineHighlightFilterOptionEnum.SHOW_ALL) {
        return true;
      }
      if (this.currentInlineFilter === InlineHighlightFilterOptionEnum.MY_HIGHLIGHTS) {
        return hl.user_id === userId;
      }
      return false;
    });

    const selectedOptions = this.findSelectedHighlightsFilterOptions();

    if (!selectedOptions.length) {
      return;
    }

    this.highlightList = this.highlightList.filter(c => {
      return selectedOptions.some(o => {
        if (o.item === PatentCommentsFilterEnum.ALL) {
          return true;
        }

        if (o.itemType === TreeFilterItemType.TEAM_USER) {
          return c.user_id === (o.item as TeamUser).id;
        }

        return true;
      });
    });
  }

  refreshHighlights() {
    this.originalHighlights.forEach((hl) => {
      this.removeHighlightsById(hl.id, false);
    });
    this.highlightList.forEach((hl) => {
      this.setHighlightSavedAnnotation(hl);
    });
  }

  resetHighlightsFilterOptions(selectedDefault: boolean) {
    this.updateCreatedByFilterOption();
    const selectedNodes = this.findSelectedHighlightsFilterOptions();
    for (let selectedNode of selectedNodes) {
      selectedNode.selected = false;
    }
    if (selectedDefault) {
      this.setDefaultCommentsFilterOption();
    }
  }

  setDefaultCommentsFilterOption() {
    this.defaultSelectedHighlightsFilterOption.selected = true;
  }

  updateCreatedByFilterOption() {
    TreeFilterUtil.updateCreatedByFilterOption(
      this.highlightsFilterOptions,
      FilterHighlightsOptionEnum.CREATED_BY,
      this.userService.getUser()?.profile?.id,
      this.originalHighlights,
      this.userTitlePipe,
      this.userService.profileAsTeamUser(),
      this.defaultSelectedHighlightsFilterOption
    );
  }

  findSelectedHighlightsFilterOptions(): TreeFilterOption[] {
    return TreeFilterUtil.findSelectedFilterOptions(this.highlightsFilterOptions);
  }

  findHighlightsFilterOption(op: TreeFilterOption): TreeFilterOption {
    return TreeFilterUtil.findFilterOption(op, this.highlightsFilterOptions);
  }

  findParentHighlightsFilterOption(op: TreeFilterOption): TreeFilterOption {
    return TreeFilterUtil.findParentFilterOption(op, this.highlightsFilterOptions);
  }

  selectHighlightsFilterOption(option: TreeFilterOption) {
    TreeFilterUtil.selectFilterOption(option, this.highlightsFilterOptions, this.defaultSelectedHighlightsFilterOption);
  }

  hasSelectedHighlightsFilterChildren(option: TreeFilterOption) {
    return TreeFilterUtil.hasSelectedFilterChildren(option, this.highlightsFilterOptions);
  }

  isTreeFilterOptionEqual(a: TreeFilterOption, b: TreeFilterOption): boolean {
    return TreeFilterUtil.isTreeFilterOptionEqual(a, b);
  }

  isHighlightsFilterByDefault() {
    return TreeFilterUtil.isFilterByDefault(this.highlightsFilterOptions, this.defaultSelectedHighlightsFilterOption);
  }

  isHighlightsSortByDefault() {
    return this.highlightsSortBy === SortHighlightsOptionEnum.RECENTLY_ADDED;
  }

  sortHighlights() {
    this.highlightList.sort((a, b) => {
      switch (this.highlightsSortBy) {
        case SortHighlightsOptionEnum.RECENTLY_ADDED:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case SortHighlightsOptionEnum.OLDEST_ADDED:
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case SortHighlightsOptionEnum.CREATOR:
          return this.userTitlePipe.transform(a.user).localeCompare(this.userTitlePipe.transform(b.user));
        case SortHighlightsOptionEnum.COLOR:
          return a.label.color.localeCompare(b.label.color);
      }
    });
  }
}
