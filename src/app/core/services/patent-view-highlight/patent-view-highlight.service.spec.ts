import { TestBed } from '@angular/core/testing';

import { PatentViewHighlightService } from './patent-view-highlight.service';
import { SharedModule } from '@shared/shared.module';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { UserTitlePipe } from '@core';
import { provideMatomo } from 'ngx-matomo-client';

describe('PatentViewHighlightService', () => {
  let service: PatentViewHighlightService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        SharedModule,
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        UserTitlePipe,
        provideMatomo({siteId: '', trackerUrl: '', disabled: true})
      ]
    });
    service = TestBed.inject(PatentViewHighlightService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
