import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';

import { PatentViewHighlightService } from './patent-view-highlight.service';
import { UserTitlePipe } from '@core/pipes';

describe('PatentViewHighlightService', () => {
  let service: PatentViewHighlightService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        RouterModule.forRoot([])
      ],
      providers: [
        provideMatomo({ siteId: '', trackerUrl: '', disabled: true }),
        UserTitlePipe
      ]
    });
    service = TestBed.inject(PatentViewHighlightService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
