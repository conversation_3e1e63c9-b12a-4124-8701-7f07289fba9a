import { Injectable } from '@angular/core';
import { Publication, TaskModel } from '@core/models';
import { Patent } from '@core/models/patent.model';
import { BehaviorSubject } from 'rxjs';
import { FilterRelevanceOptionEnum } from '@patent/types';
import { DocumentAnnotation } from '../annotation/types';
import { PaginationMetadata } from '../semantic-search';
import {
  RatingNavigationParam,
  FilterRatingsOptionEnum,
  SortRatingsOptionEnum
} from '@patent/patent-ratings/shared/types';
import { OctiQuestion } from './../octi-ai/types';

@Injectable({
  providedIn: 'root'
})
export class PatentViewService {
  previousRoute = '';
  patent: Patent;
  isFocalPatent: boolean;
  hasResultList: boolean;
  documents: any[];
  searchParams = {};
  publication: Publication;
  pagination: PaginationMetadata;

  isLoadingRatings: boolean = true;
  isCountingRatings: boolean = false;

  currentRelevanceFilter = FilterRelevanceOptionEnum.ALL;

  currentRatingsFilter: FilterRatingsOptionEnum | any = FilterRatingsOptionEnum.ALL;
  currentRatingsSort = SortRatingsOptionEnum.RECENTLY_ADDED;

  private documentIdSubject = new BehaviorSubject<number>(null);
  readonly $documentId = this.documentIdSubject.asObservable();
  private publicationNameSubject = new BehaviorSubject<string>(null);
  readonly publicationName$ = this.publicationNameSubject.asObservable();

  private refreshTasksSubject = new BehaviorSubject<boolean>(false);
  readonly refreshTasks$ = this.refreshTasksSubject.asObservable();

  private refreshCountTasksSubject = new BehaviorSubject<boolean>(false);
  readonly refreshCountTasks$ = this.refreshCountTasksSubject.asObservable();

  private showTaskViewSubject = new BehaviorSubject<TaskModel>(null);
  readonly showTaskView$ = this.showTaskViewSubject.asObservable();

  private refreshRatingsSubject = new BehaviorSubject<boolean>(false);
  readonly refreshRatings$ = this.refreshRatingsSubject.asObservable();

  private refreshCountRatingsSubject = new BehaviorSubject<boolean>(false);
  readonly refreshCountRatings$ = this.refreshCountRatingsSubject.asObservable();

  private ratingNavigationViewSubject = new BehaviorSubject<RatingNavigationParam>(null);
  readonly ratingNavigation$ = this.ratingNavigationViewSubject.asObservable();

  private showCommentViewSubject = new BehaviorSubject<DocumentAnnotation>(null);
  readonly showCommentView$ = this.showCommentViewSubject.asObservable();
  currentListIndex: number = 0;

  private octiAIPopperSubject = new BehaviorSubject<OctiQuestion>(null);
  readonly octiAIPopper$ = this.octiAIPopperSubject.asObservable();

  constructor() {
  }

  get activeDocumentId(): number {
    return this.documentIdSubject.getValue()
  }

  get activePublicationName(): string {
    return this.publicationNameSubject.getValue()
  }

  previousRouteIsCollection(): boolean {
    return this.previousRoute && this.previousRoute.indexOf('collections') > 0;
  }

  previousRouteIsMonitor(): boolean {
    return this.previousRoute && this.previousRoute.indexOf('monitor') > 0;
  }

  previousRouteIsLandscape(): boolean {
    return this.previousRoute && this.previousRoute.indexOf('landscape') > 0;
  }

  previousRouteIsLandscapeDraft(): boolean {
    return this.previousRoute && this.previousRoute.indexOf('landscape/draft') > 0;
  }

  reset(){
    this.hasResultList = false;
    this.isFocalPatent = false;
    this.currentListIndex = 0;
    this.documents = [];
    this.patent = undefined;
    this.publication = undefined;
    this.searchParams = {};
    this.previousRoute = '';
    this.documentIdSubject.next(undefined);
    this.publicationNameSubject.next(undefined);
  }

  getPatentViewerBaseUrl(documentId: number, publicationNumber?: string): string {
    if(publicationNumber){
      return `/patent/view/${documentId}/publication/${publicationNumber}`;
    }
    return `/patent/view/${documentId}`;
  }

  get refreshTasks(): boolean {
    return this.refreshTasksSubject.getValue();
  }

  set refreshTasks(val: boolean) {
    this.refreshTasksSubject.next(val);
  }

  get refreshCountTasks(): boolean {
    return this.refreshCountTasksSubject.getValue();
  }

  set refreshCountTasks(val: boolean) {
    this.refreshCountTasksSubject.next(val);
  }

  get showTaskView(): TaskModel {
    return this.showTaskViewSubject.getValue();
  }

  set showTaskView(val: TaskModel) {
    this.showTaskViewSubject.next(val);
  }

  get refreshRatings(): boolean {
    return this.refreshRatingsSubject.getValue();
  }

  set refreshRatings(val: boolean) {
    this.refreshRatingsSubject.next(val);
  }

  get refreshCountRatings(): boolean {
    return this.refreshCountRatingsSubject.getValue();
  }

  set refreshCountRatings(val: boolean) {
    this.refreshCountRatingsSubject.next(val);
  }

  get ratingNavigation(): RatingNavigationParam {
    return this.ratingNavigationViewSubject.getValue();
  }

  set ratingNavigation(val: RatingNavigationParam) {
    this.ratingNavigationViewSubject.next(val);
  }

  get showCommentView(): DocumentAnnotation {
    return this.showCommentViewSubject.getValue();
  }

  setShowCommentView(val: DocumentAnnotation) {
    this.showCommentViewSubject.next(val);
  }

  setActiveDocumentId(val: number) {
    this.documentIdSubject.next(val);
  }

  setPublicationName(val: string) {
    val = val?.replace(/[^a-zA-Z0-9]/g, '');
    this.publicationNameSubject.next(val);
  }

  getOctiAIPopper(): any {
    return this.octiAIPopperSubject.getValue();
  }

  setOctiAIPopper(val: any) {
    this.octiAIPopperSubject.next(val);
  }
}
