import { TestBed } from '@angular/core/testing';

import { AnnotationService } from './annotation.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { provideMatomo } from 'ngx-matomo-client';
import { UsersTitleTextPipe, UserTitlePipe } from '@core';

describe('AnnotationService', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports: [HttpClientTestingModule, RouterModule.forRoot([])],
    providers:[
      provideMatomo({siteId: '', trackerUrl: '', disabled: true }),
      UsersTitleTextPipe,
      UserTitlePipe,
    ]
  }));

  it('should be created', () => {
    const service: AnnotationService = TestBed.inject(AnnotationService);
    expect(service).toBeTruthy();
  });
});
