import { Patent, TeamUser } from '@core/models';

export enum DocumentCommentSourceEnum {
  ANNOTATION = "annotation",
  GENERAL = "general"
}


export interface Label {
  id?: number;
  user_id?: number;
  name?: string;
  shortcut?: string;
  color?: string;
  created_at?: Date;
  document_counts?: number;
  is_highlight?: boolean;
}

export interface Page {
  current_page: number;
  page_size: number;
  total_hits: number;
  last_page: number;
}

export interface LabelsResponse {
  page: Page;
  labels: Array<Label>;
}

export interface DocumentAnnotation {
  id?: number;
  end_pos?: number;
  field?: string;
  start_pos?: number;
  parent_comment_id ?: number;
  text?: string;
  color?: string;
  comment?: string;
  parsed_comment?: string;
  comment_text_only?: string;
  label_id?: number;
  created_at?: string;
  updated_at?: string;
  document_id?: number;
  publication_number?: string;
  label?: Label;
  user_id?: number;
  user?: TeamUser;
  tagged_users?: TeamUser[];
  tagged_user_ids?: number[];
  tagged_group_ids?: number[];
  private?: boolean;
  show_replies?: boolean;
  is_displayed?: boolean;
  replies?: Array<Partial<DocumentAnnotation>>;
  source?: DocumentCommentSourceEnum;
}

export interface DocumentLabel {
  id?: number;
  end_pos: number;
  field: string;
  start_pos: number;
  text: string;
  label_id: number;
  created_at?: Date;
  label?: Label;
  user_id?: number;
  user?: TeamUser;
}

export interface DocumentAnnotationResponse {
  status: number;
  data: DocumentAnnotation;
}

export interface DocumentLabelResponse {
  status: number;
  data: DocumentAnnotation[];
}

export interface LabelsAndCommentsResponse {
  status: number;
  data: {
    comments: Array<DocumentAnnotation>;
    labels: Array<DocumentAnnotation>;
  };
}

export interface FullDocumentAnnotationResponse {
  document: Patent;
  comments: Array<DocumentAnnotation>;
  labels: Array<DocumentLabel>;
}

export interface DocumentsResponse {
  documents: Array<FullDocumentAnnotationResponse>;
  page: Page;
}

export interface AnnotationPosition {
  startPos: number,
  endPos: number,
  startNode?: Node,
  endNode?: Node,
  foundStart?: boolean,
  foundEnd?: boolean
}
