import { Injectable } from '@angular/core';
import { BehaviorSubject, from, Observable, of, ReplaySubject } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { IpLoungeService } from '../ip-lounge/ip-lounge.service';

import {
  DelegationSourceEnum,
  SubscriptionType,
  TeamUser, TeamUserTypeEnum,
  User,
  UserProfile,
  UserStatistics,
  UserStatusEnum,
  UserSubscription
} from '../../models';
import { ApiService } from '../api/api.service';
import { JwtService } from '../jwt/jwt.service';
import {
  ChangePasswordRequest,
  ConfirmSignupRequest,
  ForgotPasswordRequest,
  ResendMailConfirmationRequest,
  ResetPasswordRequest,
  SigninRequest,
  SignupRequest
} from './requests';
import { Router } from '@angular/router';
import { PaginationMetadata } from '../semantic-search';
import { Locale, locales } from '@core/utils';
import { MatomoService } from '../matomo/matomo.service';
import { featureType } from './types';
import { ToastService } from '../toast/toast.service';
import { ToastTypeEnum } from '../toast/types';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private userSubject = new BehaviorSubject<User>({} as User);
  public user = this.userSubject.asObservable();

  private isAuthenticatedSubject = new ReplaySubject<boolean>(1);
  public isAuthenticated = this.isAuthenticatedSubject.asObservable();

  private permissionSettingChangedSubject = new ReplaySubject<Record<string, boolean>>(1);
  public permissionSettingChanged$ = this.permissionSettingChangedSubject.asObservable();

  private _teamUsers: TeamUser[] = [];
  private cachedAvatars = {};

  static DEFAULT_COMMENT_PERMISSION_KEY = 'default_comment_permission';

  get teamUsers(): TeamUser[] {
    return this._teamUsers;
  }

  private set teamUsers(users: TeamUser[]) {
    this._teamUsers = users;
  }

  private _userStatistics: UserStatistics;
  get userStatistics(): UserStatistics {
    return this._userStatistics;
  }
  set userStatistics(value: UserStatistics) {
    this._userStatistics = value;
  }

  constructor(
    private apiService: ApiService,
    private jwtService: JwtService,
    private router: Router,
    private ipLoungeService: IpLoungeService,
    private toastService: ToastService,
    private matomoService: MatomoService
  ) {
  }

  // whether user has monitor flag
  get hasMonitor() : boolean { return this.hasFeature('monitor'); }

  get hasAIAssistant() : boolean { return this.hasFeature('ai assistant'); }

  get hasBinaryRating(): boolean {
    return this.hasFeature("Binary rating", false);
  }

  get userId(): number {
    return this.getUser()?.profile?.id;
  }

  public populate(): Promise<User> {
    return new Promise((resolve, reject) => {

      if (!this.jwtService.getAccessToken()) {
        this.purgeUser();
        return resolve(null);
      }

      this.apiService.get('auth/user/profile').subscribe({
        next: ({data}) => {
          this.setUser({subscription: data.subscription, profile: data.user});
          if (data.user && data.user.must_change_password) {
            this.router.navigate(['/auth/change-password']).then();
          } else {
            return resolve(this.userSubject.getValue());
          }
        },
        error: err => {
          this.purgeUser();
          return resolve(null);
        }
      });
    });
  }

  public setUser(user: User): void {
    this.userSubject.next(user);
    this.isAuthenticatedSubject.next(true);
    if (user?.profile) {
      this.matomoService.login(user.profile.email);
    }
  }

  public userAuthenticated(): boolean {
    return !!this.jwtService.getAccessToken();
  }

  public getUser(): User {
    return this.userSubject.getValue();
  }

  public canUserAnnotate(): boolean {
    const user = this.getUser();
    return user && user.subscription && user.profile && user.subscription.type
      && user.subscription.type !== SubscriptionType.External;
  }

  public isAdmin(): boolean {
    const user = this.getUser();
    return user?.profile?.is_admin;
  }

  public isManager(): boolean {
    const user = this.getUser();
    return user?.profile?.is_manager;
  }

  public isFreeUser(): boolean {
    const user = this.getUser();
    return user && user.subscription && user.subscription.type === SubscriptionType.Free;
  }

  public isCollaboratorUser(): boolean {
    const user = this.getUser();
    return user?.subscription?.type === SubscriptionType.Collaborator;
  }

  public isIpLounge(): boolean {
    const user = this.getUser();
    return user?.profile?.delegation_source === DelegationSourceEnum.IP_LOUNGE;
  }

  public isDelegatedUser(): boolean{
    const user = this.getUser();
    return user?.profile?.status === UserStatusEnum.DELEGATED;
  }

  isFreeTeamUser(u: TeamUser){
    return u?.subscription?.type && u.subscription.type === SubscriptionType.Free;;
  }

  isPremiumTeamUser(u: TeamUser){
    return u?.subscription?.type && [SubscriptionType.Free, SubscriptionType.Collaborator].indexOf(u.subscription.type)===-1;
  }

  isPremiumUser() {
    return this.getUser()?.subscription?.type !== SubscriptionType.Free;
  }

  isFirstTimeExpandSearchInput(): boolean {
    return this.profile?.ui_settings['semantic_execute_expanded_search_input'] === undefined;
  }

  public getSubscriptionMaxLimit(): number {
    return this.getUser()?.subscription?.api_max_result;
  }

  public updateProfile(profile: UserProfile): Observable<Object> {
    const payload = {
      company_name: profile.company_name, department_name: profile.department_name, gdpr: profile.gdpr,
      first_name: profile.first_name, last_name: profile.last_name, country: profile.country,
      phone1: profile.phone1, phone2: profile.phone2, website: profile.website, mobile: profile.mobile,
      ui_settings: profile.ui_settings, save_history: profile.save_history, newsletter: profile.newsletter,
      locale: profile.locale
    };
    return this.apiService.patch('auth/user/profile', payload).pipe(map(response => response));
  }

  public saveAvatar(userId: number, image: Blob): Observable<string> {
    const formData: FormData = new FormData();
    formData.append('file', image);
    return this.apiService.postFormData('auth/user/image', formData);
  }

  public deleteAvatar(): Observable<any> {
    return this.apiService.delete('auth/user/image');
  }

  public setCachedAvatar(userId: number, blob: Blob): void {
    this.cachedAvatars[userId.toString()] = blob;
  }

  public getCachedAvatar(userId: number): Blob {
    return this.cachedAvatars[userId.toString()];
  }

  public isAvatarCached(userId: number): boolean {
    return Object.keys(this.cachedAvatars).includes(userId.toString());
  }

  public getAvatar(userId: number): Observable<Blob> {
    if (this.isAvatarCached(userId)) {
      return of(this.getCachedAvatar(userId));
    }

    return this.apiService.getImage(`auth/user/${userId}/image`)
      .pipe(
        tap((blob) => this.setCachedAvatar(userId, blob)),
        catchError((error) => {
          this.setCachedAvatar(userId, null);
          throw error;
        })
      );
  }

  public purgeUser(): void {
    this.jwtService.destroyTokens();
    this.userSubject.next({} as User);
    this.isAuthenticatedSubject.next(false);
    this.ipLoungeService.removeIpLoungeToken();
    this.matomoService.logout();
  }

  public signup(payload: SignupRequest): Observable<Object> {
    return this.apiService.post('auth/signup', payload)
      .pipe(
        tap(response => this.setToken(response.data.access_token)),
        map(response => response)
      );
  }

  public signin(payload: SigninRequest): Observable<Object> {
    return from(this.apiService
      .post('auth/login', payload)
      .toPromise()
      .then(response => {
        this.jwtService.saveAccessToken(response.data.access_token);
        this.jwtService.saveRefreshToken(response.data.refresh_token);
        return Promise.resolve(response);
      })
      .then(() => this.populate())
      .then(user => user)
    );
  }

  public setToken(token: string): Promise<User> {
    this.jwtService.saveAccessToken(token);
    return this.populate();
  }

  public signout(): Observable<Object> {
    return this.apiService.delete('auth/logout').pipe(
      tap(() => this.purgeUser())
    );
  }

  public changePassword(payload: ChangePasswordRequest): Observable<Object> {
    return this.apiService.put('auth/password/change', payload);
  }

  public forgotPassword(payload: ForgotPasswordRequest): Observable<Object> {
    return this.apiService.post('auth/password/forgot', payload)
      .pipe(map(response => response));
  }

  public resetPassword(payload: ResetPasswordRequest): Observable<Object> {
    return this.apiService.put('auth/password/reset', payload)
      .pipe(map(response => response));
  }

  public refreshToken(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.apiService.post('auth/refresh').toPromise().then(response => {
        this.jwtService.saveAccessToken(response.data.access_token);
        resolve();
      }).catch(res => reject());
    })
  }

  public resendMailConfirmation(
    payload: ResendMailConfirmationRequest
  ): Observable<Object> {
    return this.apiService.post('auth/request_confirmation', payload)
      .pipe(map(response => response));
  }

  public confirmSignup(payload: ConfirmSignupRequest): Observable<any> {
    return this.apiService.get(`auth/signup_confirmation/${payload.token}`)
      .pipe(map(response => response));
  }

  public deleteAccount(): Observable<any> {
    return this.apiService.delete('auth/user/profile')
      .pipe(map(response => response));
  }

  public getStatistics(): Observable<UserStatistics> {
    return this.apiService.get('auth/user/statistics')
      .pipe(map(response => {
        this.userStatistics = response.data;
        return response.data;
      }));
  }

  public logActivity(activityKey: string): Observable<any> {
    return this.apiService.post('auth/user/statistics/logs', {type: activityKey}, {}, false);
  }

  public updateSubscription(user_id: number, type: SubscriptionType): Observable<any> {
    return this.apiService.patch(`auth/user/${user_id}/subscription`, {type: type})
      .pipe(map(response => response));
  }

  public formatName(user: UserProfile): string {
    if (!user) {
      return '';
    } else if (user.first_name || user.last_name) {
      return `${user.first_name || ''} ${user.last_name || ''} (${user.email})`.trim();
    } else {
      return user.email;
    }
  }

  public getDefaultFilters(user: UserProfile): Object {
    if (!user.ui_settings) {
      user.ui_settings = {};
    }
    if (!('default_filters' in user.ui_settings)) {
      user.ui_settings['default_filters'] = {
        authorities_plus: '',
        authorities_minus: 'CN,KR,JP',
      };
    }
    return user.ui_settings['default_filters'];
  }

  /**
   * whether use has specific feature
   * @param feature feature name
   * @param includeAdmin show feature to admin, by default, feature will be shown to admin users
   */
  hasFeature(feature: string, includeAdmin: boolean = true): boolean {
    const profile = this.getUser()?.profile;
    return (includeAdmin && profile?.is_admin) || profile?.features?.findIndex(o => o.name.toLowerCase() === feature?.toLowerCase()) > -1;
  }

  canUseWorkflowFeature(): boolean {
    return !!(this.getUser()?.profile?.company_id);
  }

  canUseSaveAndShareFeature(func: 'save' | 'share'): boolean {
    if(this.hasFeature('Disable Save And Share', false)){
      switch(func){
        case 'save':
          this.toastService.show({
            header: 'Achtung: Save nicht gestattet!',
            body: `Gemäss einer internen Weisung des IGE darf die Funktion «Save document(s)» in Octimine nicht verwendet werden.`,
            type: ToastTypeEnum.INFO,
            closable: true,
            autohide: false
          });
          break;
        case 'share':
          this.toastService.show({
            header: 'Achtung: Sharing nicht gestattet!',
            body: `Gemäss einer internen Weisung des IGE darf die Funktion «Share document(s)» in Octimine nicht verwendet werden.`,
            type: ToastTypeEnum.INFO,
            closable: true,
            autohide: false
          });
          break;
      }
      console.log('has feature')
      return false;
    }
    return true;
  }

  canManageGroup(): boolean {
    return this.isManager() && this.canUseWorkflowFeature();
  }

  hasTaskFeature(): boolean {
    return this.canUseWorkflowFeature();
  }

  hasTagFeature(): boolean {
    return this.canUseWorkflowFeature() || (this.isNotExternalUser() && !this.isFreeUser());
  }

  hasOctiAiFeature(): boolean {
    return this.isNotExternalUser() && !this.isFreeUser();
  }

  /**
   * save/update a particular value in user ui-setting object
   * @param settings An object instance of a setting
   */
  public updateUISettings(settings: {}): Observable<User> {
    const ui_settings = this.getUser()?.profile?.ui_settings ? {...this.getUser().profile.ui_settings} : {};
    Object.assign(ui_settings, settings);
    return this.apiService.patch('auth/user/profile', {ui_settings})
      .pipe(
        map(({data}) => {
          return {subscription: data.subscription, profile: data.user} as User;
        }),
        tap((user: User) => {
          this.setUser(user);
        })
      );
  }

  createExternalUser(accessToken: string) {
    const subscription = new UserSubscription();
    subscription.type = SubscriptionType.External;

    const user = new User();
    user.subscription = subscription;
    this.jwtService.saveAccessToken(accessToken);
    this.setUser(user);
  }

  isNotExternalUser(): boolean {
    return !!this.getUser()?.subscription?.type && this.getUser()?.subscription?.type !== SubscriptionType.External;
  }

  isExternalUser(): boolean {
    return this.getUser()?.subscription?.type === SubscriptionType.External;
  }

  getTeamUsers(payload, cache: boolean = false): Observable<{ page: PaginationMetadata, users: Array<TeamUser> }> {
    return this.apiService.get('auth/users/team', payload)
      .pipe(
        map(response => response.data),
        tap(({users}) => {
          if (cache) {
            this.teamUsers = users;
          }
        })
      );
  }

  getTeamUser(userId: number): Observable<TeamUser> {
    return this.apiService.get(`auth/users/team/${userId}`)
      .pipe(map(response => response.data));
  }

  getLocale(): Locale {
    let locale: Locale;
    if (this.getUser()?.profile?.locale) {
      locale = locales.find(fmt => fmt.language === this.getUser().profile.locale);
    } else {
      locale = locales.find(fmt => fmt.language === navigator.language);
    }

    if (!locale) {
      return locales.find( lc => lc.language === 'en-GB');
    }
    return locale;
  }

  loginIPLounge(token: string): Observable<Object> {
    return from(this.apiService
      .post('auth/login_ip_lounge', {token: token})
      .toPromise()
      .then(response => {
        this.jwtService.saveAccessToken(response.data.access_token);
        this.jwtService.saveRefreshToken(response.data.refresh_token);
        this.ipLoungeService.saveIPLoungeToken(token, response.data.services);
        return Promise.resolve(response);
      })
      .then(() => this.populate())
      .then(user => user)
    );
  }

  updateUser(userId: number, payload): Observable<User> {
    return this.apiService.patch(`auth/user/${userId}`, payload)
      .pipe(map(({data}) => data));
  }

  createUser(payload): Observable<User> {
    return this.apiService.get('auth/user/', payload)
      .pipe(map(({data}) => data));
  }

  inviteUser(email: string): Observable<any> {
    return this.apiService.post('auth/users/invite', {email: email});
  }

  acceptInvitation(payload: any): Observable<any> {
    return this.apiService.patch('auth/users/invite', payload);
  }

  removeUserFromCompany(userId: number): Observable<any> {
    return this.apiService.delete(`auth/users/invite/${userId}`);
  }

  canCreateLandscapeProfile(): boolean {
    return this.notInDelegationSources([DelegationSourceEnum.IP_LOUNGE]);
  }

  canCreateMonitorProfile(): boolean {
    return this.notInDelegationSources([DelegationSourceEnum.IP_LOUNGE]);
  }

  enable2FA(): Observable<Object> {
    return this.apiService.get('auth/two_factor_setup');
  }

  activate2FA(payload: {totp_uri: string, verification_code: string}): Observable<Object> {
    return this.apiService.put('auth/two_factor_setup', payload);
  }

  disable2FA(code: string): Observable<Object> {
    const options = {
      body: {verification_code: code}
    };
    return this.apiService.delete('auth/two_factor_setup', options);
  }

  getUISetting(field: string, defaultValue: any) {
    const settings = this.getUser()?.profile?.ui_settings;
    if (settings && field in settings) {
      return settings[field];
    }
    return defaultValue;
  }

  /**
   * Only the users who don't belong to any company or are admin or manager can manage the labels
   */
  canManageLabels(): boolean {
    const profile = this.getUser()?.profile;
    if (profile) {
      return  this.isPremiumUser();
    }
    return false;
  }

  private notInDelegationSources(forbiddenDelegationSources: DelegationSourceEnum[]): boolean {
    const profile = this.getUser()?.profile;
    return profile && !forbiddenDelegationSources.includes(profile.delegation_source);
  }

  private get profile(): any {
    if (!this.getUser()) {
      return null;
    }
    return this.getUser().profile;
  }

  addChartCategory(section: featureType ): Observable<{ name: string, charts: Array<string>, index: number }> {
    const user = this.profile;
    if (!user) {
      return
    }

    const chartCategories = this.getChartCategories(section);
    const chartCategoryIndices = chartCategories.map((d) => {
      const matched = d.name.match(/^Dashboard (\d+)$/);
      if (matched) {
        return parseInt(matched[1]);
      }
      return 0;
    });
    const maxOrder = chartCategoryIndices.length > 0 ? Math.max(...chartCategoryIndices) : 0;
    const indices = chartCategories.map((d) => {
      return d.index || 0;
    });
    const maxIndex = chartCategories.length > 0 ? Math.max(...indices) + 1 : 0;
    const chartCategory = {
      name: `Dashboard ${maxOrder + 1}`,
      charts: [],
      index: maxIndex
    }
    chartCategories.push(chartCategory);
    const settings = {
      [section + '_dashboards']: chartCategories
    }

    return this.updateUISettings(settings)
      .pipe(
        map(() => chartCategory),
        catchError((err) => {
          console.log(err);
          throw err;
        })
      );
  }

  public getChartCategories(section: featureType): { name: string, charts: Array<string>, index: number }[] {
    const user = this.profile;
    if (!user) {
      return [];
    }
    if (!user.ui_settings) {
      user.ui_settings = {};
    }
    let chart_setting = section + "_dashboards";
    if (!(chart_setting in user.ui_settings)) {
      const singleDashboardCharts = this.getSingleDashboardCharts(section);
      user.ui_settings[chart_setting] = singleDashboardCharts ? [{name: 'Dashboard 1', index: 0,
                                                                  charts:  singleDashboardCharts
                                                                 }] : [];
    }
    return user.ui_settings[chart_setting];
  }

  public getActiveChartCategory(section: featureType , categoryName: string): { name: string, charts: Array<string>, index: number } {
    if (!categoryName) {
      return null;
    }
    const user = this.profile;
    if (!user.ui_settings) {
      user.ui_settings = {};
    }
    const chartCategories = this.getChartCategories(section);
    if (!chartCategories.length) {
      return null;
    }
    return chartCategories.find(category => category.name === categoryName);
  }

  private getSingleDashboardCharts(section = 'semantic'): Array<string> {
    const chart_setting = section === 'semantic' ? 'dashboard_charts' : 'landscape_dashboard';
    if (!(chart_setting in this.profile.ui_settings)) {
      return null;
    }
    return this.profile.ui_settings[chart_setting];
  }

  clearStoredData(): void {
    this.userSubject.next({} as User);
    this.isAuthenticatedSubject.next(false);
    this.teamUsers = [];
    this.userStatistics = null;
  }

  get defaultCommentPermission(): boolean {
    return this.getUISetting(UserService.DEFAULT_COMMENT_PERMISSION_KEY, true);
  }

  updatePermissionSetting(key: string, value: boolean): Observable<User>{
    return this.updateUISettings({[key]: value})
      .pipe(
        tap(() => this.permissionSettingChangedSubject.next({[key]: value}))
      );
  }

  get messageCollaboratorQuotas(): string {
    if (!this.isCollaboratorUser() || !this.userStatistics) {
      return '';
    }
    if (!this.userStatistics.monthly_quota) {
      return 'Performing searches is not possible for you, but you still have full access to any patent saved or shared.';
    } else {
      return `You have ${this.userStatistics.remaining_quota} searches remaining this month. You also have full access to any patent saved or shared.`;
    }
  }

  get isCollaboratorQuotasExceeded(): boolean {
    if (!this.isCollaboratorUser() || !this.userStatistics) {
      return false;
    }

    if (!this.userStatistics.monthly_quota || this.userStatistics.remaining_quota == 0) {
      return true;
    }

    return false;
  }

  isMe(user: TeamUser): boolean {
    if (!user || user.type === TeamUserTypeEnum.GROUP) {
      return false;
    }
    return this.getUser()?.profile?.id === user.id;
  }

  isResourceOwner(resourceUserId: number): boolean {
    return this.getUser()?.profile?.id === resourceUserId;
  }

  profileAsTeamUser(): TeamUser {
    const profile = this.getUser()?.profile;
    if (!profile) {
      return null;
    }
    return {
      id: profile.id,
      email: profile.email,
      first_name: profile.first_name,
      last_name: profile.last_name,
      type: TeamUserTypeEnum.USER
    } as TeamUser;
  }
}
