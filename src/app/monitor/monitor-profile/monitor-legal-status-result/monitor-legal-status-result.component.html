<div class="container">
  <div class="monitor-run--section" *ngIf="monitorRuns">
    <h4 class="profile-headline">Monitoring legal status</h4>
    <div class="row custom-result-section mb-5" *ngIf="user?.is_admin && userService.canCreateMonitorProfile()">
      <div class="col-md-12">
        <div class="row align-items-center">
        <div class="col-auto">
          <label >Generate results:</label>
        </div>
        <div class="col-auto">
        <button type="button" class="btn btn-primary btn-md ms-3" (click)="createMonitorResultProfile()"
        ngbTooltip="Create result set">Generate</button>
          </div>
        </div>
      </div>
    </div>
    <table class="table table-hover monitor-run-table" [hidden]="monitorRuns?.monitor_runs.length===0">
      <thead >
        <tr>
          <th scope="col" class="text-center">#</th>
          <th scope="col">Reported period</th>
          <th scope="col">Number of changes</th>
          <th scope="col">Notification sent</th>
          <th scope="col"><i class="fas fa-tools"></i></th>
        </tr>
      </thead>
      <tbody>
        <tr [class]="'cursor-pointer  monitor-run '+singleRun.status"
          *ngFor="let singleRun of monitorRuns?.monitor_runs;index as indexI"
          [ngClass]="{'table-active open-sans-bold':singleRun.id==storeService.selectedMonitorRun}">
          <td scope="row" class="text-center open-sans-bold" (click)="getMonitorResultProfile(singleRun)">
            {{(monitorRuns.page.page_size*(monitorRuns.page.current_page-1))+indexI+1}}
          </td>
          <td (click)="getMonitorResultProfile(singleRun)">{{singleRun.name | dateFormat}}</td>
          <td (click)="getMonitorResultProfile(singleRun)" class="monitor-run-result-count">
            {{singleRun.number_of_documents.LEGAL_STATUS}}
          </td>
          <td (click)="getMonitorResultProfile(singleRun)" class="monitor-run-result-date">
            {{singleRun.created_at | dateFormat: 'ShortDate'}}</td>
          <td>
            <a class="btn btn-primary btn-sm btn-icon"
              *ngIf="singleRun.status==='Finished' || singleRun.status==='Error'"
              (click)="deleteMonitorResultProfile(singleRun)" ngbTooltip="Click here to delete this result set">
              <i class="fas fa-trash-alt"></i>
            </a>
          </td>
        </tr>
      </tbody>
    </table>
    <div *ngIf="monitorRuns.page.last_page>1">
      <app-pagination class="d-flex justify-content-end" [pagination]="monitorRuns.page"
        (navigatePage)="navigateMonitorRun($event)"></app-pagination>
      <br>
    </div>
  </div>
  <app-alert type="info" *ngIf="monitorRuns?.monitor_runs?.length === 0" message="No results generated yet for this profile."></app-alert>
</div>

<app-patent-list-layout *ngIf="monitorRuns?.monitor_runs?.length > 0"
                        id="monitor-results-container"
                        [storeService]="storeService"
                        [documents]="documents"
                        [isLoading]="loading || loadingResult"
                        [showDashboardActionBar]="true"
                        [alwaysShowDocumentsControlBar]="true"
                        (viewModeChange)="onChangeViewMode($event)">
  <ng-container alertMessages [ngTemplateOutlet]="noDocumentsMessage"></ng-container>

  <ng-container documentsControlBar [ngTemplateOutlet]="monitorControlBar"></ng-container>

  <ng-container documentsTable [ngTemplateOutlet]="monitorList"></ng-container>

  <ng-container documentsVisual>
    <app-charts-container [isColumnLayout]="isCombinedMode" [storeService]="storeService"></app-charts-container>
  </ng-container>
</app-patent-list-layout>

<app-filters-bar [showOperator]="true" [storeService]="storeService" [alwaysBeSticky]="true"></app-filters-bar>
<app-spinner *ngIf="loadingResult" id="monitor-loading" hidden></app-spinner>
<app-zoom-chart [showFavoriteOption]="false" [storeService]="storeService"></app-zoom-chart>

<ng-template #monitorControlBar>
  <div class="me-control-bar sticky-top" *ngIf="monitorRuns && !loadingResult && hasDocuments">
    <div class="container-fluid d-flex justify-content-between align-items-center">
      <app-patent-control-bar [searchService]="monitorService" [columnsToShow]="columnsToShow"
                              [hasHarmonizeControl]="false" [hasExportControl]="hasDocuments" [exportDisplayOptions]="['csv','xlsx','pdf']"
                              [exportAdditionalParams]="additionalExportParams"
                              [hasOctiAIControl]="hasDocuments"
                              [hasSaveToCollectionControl]="hasDocuments" [saveSearchTextInput]="selectedSnapshot?.name"
                              [hasTemporaryLinkControl]="hasDocuments" (sharePatentsEvent)="onSharePatents($event)"
                              [taskShowCreationButton]="hasDocuments" (taskSaved)="onTaskSaved($event)"
                              [storeService]="storeService">
      </app-patent-control-bar>
    </div>
  </div>
</ng-template>

<ng-template #monitorList>
  <div class="container-fluid" [ngClass]="{'pe-0': isCombinedMode}">
    <div *ngIf="monitorRuns && !loadingResult" class="monitor-run-data-box" id="monitor-results">
      <div *ngIf="hasDocuments">
        <div class="d-flex justify-content-start align-items-center" data-intercom-target="Monitoring result">
          <div class="monitor-results-title new-layout-headline">
            <span [hidden]="!totalSelectedPatents" class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{storeService.pagination.total_hits}}
            <span *ngIf="scope == 'family'">
              {{ 'patent family' | pluralize: storeService.pagination.total_hits }}
              ({{storeService.pagination.total_publications}} {{ 'publication' | pluralize: storeService.pagination.total_publications }})
            </span>
            <span *ngIf="scope == 'publication'">
              {{ 'patent publication' | pluralize: storeService.pagination.total_publications }}
            </span>
          </div>
        </div>
        <app-alert type="success" *ngIf="collectionsStoreService.getSaveToCollectionSuccess()"
          [message]="collectionsStoreService.getSaveToCollectionSuccess()"></app-alert>
        <app-alert type="success" *ngIf="savedTaskMessages" [message]="savedTaskMessages"></app-alert>

        <div class="psr-patent-table">
          <app-patent-table [patents]="documents" (sort)="onSort($event)"
                            [showPatentSelection]="true" [pagination]="pagination"
                            [hasLinksToBooleanSearch]="true"
                            [hasSorting]="false"
                            [searchService]="monitorService"
                            pathUrl="/patent" [linkData]="linkData"
                            backButtonTitle="Back to monitor"
                            [showHighlight]="true"
                            [showRank]="false"
                            [storeService]="storeService">
          </app-patent-table>
        </div>

        <ng-container [ngTemplateOutlet]="monitorFooter"></ng-container>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #monitorFooter>
  <div class="d-flex justify-content-between align-items-center"
       *ngIf="monitorRuns && !loadingResult && hasDocuments">
    <div></div>
    <div class="d-flex justify-content-end align-items-center">
      <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)"
                     [pageOptions]="[25,50,100]"></app-page-size>
      <app-pagination [pagination]="pagination" (navigatePage)="navigatePage($event)"></app-pagination>
    </div>
  </div>
</ng-template>

<ng-template #noDocumentsMessage>
  <div *ngIf="!hasDocuments && !loading && !loadingResult" class="container-fluid my-4">
    <app-alert type="warning" [hideCloseBtn]="true" [message]="getAlertMessage()">
    </app-alert>
  </div>
</ng-template>
