<div class="d-flex flex-column justify-content-start min-vh-100">
  <app-header></app-header>

  <!-- Analysis-->
  <div class="flex-fill landscape-profiles">
    <div class="d-flex justify-content-center pt-5" *ngIf="profileStatus == 'COMPUTING'">
      <h5 class="message-computing">Please wait while your profile is being computed</h5>
    </div>
    <app-spinner *ngIf="loading"></app-spinner>
    <div *ngIf="!loading" >
      <div class="page-content container-fluid mt-3 mb-3">
        <div class="border rounded p-4 mt-3 mb-3 section-highlights">
          <!--Title-->
          <div class="card-title" *ngIf="profile">
            <div class="row d-flex align-items-center flex-wrap">
              <div class="col-md-6">
                <h3 class="mb-4">{{profile?.name}}</h3>
                <p>
                  <b>{{profile?.category}}</b> &nbsp;&nbsp;
                  <i class="far fa-clock"></i>&nbsp;<span class="text-gray">{{profile?.updated_at | dateFormat: 'ShortDate'}}</span>
                </p>
              </div>
              <div class="col-md-6">
                <div class="d-flex justify-content-end">
                  <div class="state">
                    <div class="d-flex">
                      <img src="assets/images/landscape/info-icon.svg" alt="" class="me-3">
                      <span>
                        INFORMATION<br>SETUP
                      </span>
                    </div>
                  </div>
                  <div class="state">
                    <div class="d-flex">
                      <img src="assets/images/landscape/pencil-ruler-solid.png" alt="" class="me-3"/>
                      <span>
                        CONFIRM<br>SELECTION
                      </span>
                    </div>
                  </div>
                  <div class="state active">
                    <div class="d-flex">
                      <img src="assets/images/landscape/analysis-icon.svg" alt="" class="me-3">
                      <span>
                        Analyze<br>Landscape
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--./Title-->
          <hr>
          <!--Line-->

          <!--Result boxes-->
          <div class="info-cards-wrap d-flex mb-1" >
            <div class="box-bibliographic pt-0">
              <div class="col-11 p-0">
                <div class="d-flex justify-content-between mb-2">
                  <span class="info info-label">No. of publications</span>
                  <a href="javascript:void(0)" class="info info-value" (click)="scrollToPublicationTable()">
                    {{profile?.publications_count}}
                  </a>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="info info-label">No. of families</span>
                  <a href="javascript:void(0)" class="info info-value" (click)="scrollToPublicationTable()">
                    {{profile?.families_count}}
                  </a>
                </div>
                <div class="d-flex justify-content-between mb-2" *ngIf="profile?.active_patents_count !== null">
                  <span class="info info-label">No. of alive families
                    <app-tooltip class="ms-2"
                                 tooltipTitle='Number of alive families'
                                 tooltipText='Number of families with at least one granted patent'
                                 tooltipPosition="right"
                                 tooltipIconSize="sm">
                    </app-tooltip>
                  </span>
                  <a href="javascript:void(0)" class="info info-value" style="line-height: 1.8;"
                  (click)="setHighlight('Legal status','LEGAL_STATUS','valid')">
                    {{profile?.active_patents_count}}
                  </a>
                </div>
                <div class="d-flex justify-content-between mb-2" *ngIf="profile?.inactive_patents_count !== null">
                  <span class="info info-label">No. of dead families
                    <app-tooltip class="ms-2"
                                 tooltipTitle='Number of dead families'
                                 tooltipText='Number of families with no granted or pending patents'
                                 tooltipPosition="right"
                                 tooltipIconSize="sm">
                    </app-tooltip>
                  </span>
                  <a href="javascript:void(0)" class="info info-value" style="line-height: 1.8;"
                  (click)="setHighlight('Legal status','','LEGAL_STATUS=invalid AND LEGAL_STATUS<>valid', 'Invalid', '')">
                    {{profile?.inactive_patents_count}}
                  </a>
                </div>
                <div class="d-flex justify-content-between mb-2" *ngIf="profile?.inactive_patents_count !== null">
                  <span class="info info-label">No. of unknown status families
                    <app-tooltip class="ms-2"
                                 tooltipTitle='Number of unknown status families'
                                 tooltipText='Number of families with unknown legal status'
                                 tooltipPosition="right"
                                 tooltipIconSize="sm">
                    </app-tooltip>
                  </span>
                  <a href="javascript:void(0)" class="info info-value" style="line-height: 1.8;"
                  (click)="setHighlight('Legal status', '', 'LEGAL_STATUS<>valid AND LEGAL_STATUS<>invalid', 'Unknown', '')">
                    {{profile?.unknown_legal_status_patents_count}}
                  </a>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="info info-label">No. of authorities
                    <app-tooltip class="ms-2"
                                 tooltipTitle='Number of authorities'
                                 tooltipText='Number of authorities covered by all families'
                                 tooltipPosition="right"
                                 tooltipIconSize="sm">
                    </app-tooltip>
                  </span>
                  <span class="info info-value">
                    {{profile?.covered_authorities_count}}
                  </span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="info info-label">Mean family age
                    <app-tooltip class="ms-2"
                                 tooltipTitle='Mean family age'
                                 tooltipText='Average of patent family’s years since the priority date'
                                 tooltipPosition="right"
                                 tooltipIconSize="sm">
                    </app-tooltip>
                  </span>
                  <span class="info info-value">
                    {{profile?.mean_patent_age_in_years | number:'0.0-2'}}
                  </span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span class="info info-label">Highly cited families
                    <app-tooltip class="ms-2"
                                 tooltipTitle='Highly cited families'
                                 tooltipText='Number of families with high citation numbers (top 5% of all cited patents)'
                                 tooltipPosition="right"
                                 tooltipIconSize="sm">
                    </app-tooltip>
                  </span>
                  <a href="javascript:void(0)" class="info info-value btn-highly-cited"
                    (click)="setHighlight('Top 5% highly cited','citation_forward_count',profile.highly_cited_patents_threshold, profile?.highly_cited_patents_threshold, '>=')">
                    {{profile?.highly_cited_patents_count}}
                  </a>
                </div>
                <div class="d-flex justify-content-between mb-2" *ngIf="profile?.environmental_patents_count !== null">
                  <span class="info info-label">No. of green families
                    <app-tooltip class="ms-2"
                                 tooltipTitle='Number of green families'
                                 tooltipText='Number of families with technology considered environmentally friendly'
                                 tooltipPosition="right"
                                 tooltipIconSize="sm">
                    </app-tooltip>
                  </span>
                  <a href="javascript:void(0)" class="info info-value" style="line-height: 1.8;"
                    (click)="setEnvironmentalHighlight()" *ngIf="profile?.environmental_patents_count > 0">
                    {{profile?.environmental_patents_count}}
                  </a>
                  <b *ngIf="profile?.environmental_patents_count == 0">{{profile?.environmental_patents_count}}</b>
                </div>
              </div>
            </div>
            <div class="chart-card me-4">
              <app-authorities-timeline [showHeaderBar]="false" [chartOptions]="authoritiesTimelineOptions" [exportable]="false"
                                        spinner="assets/images/octimine_blue_spinner.gif" chartClass="landscape-chart" chartItemHeight="350px" chartInstanceHeight="330px"
                                        topAuthorities="5" [sliderOptions]="null" lastYears="5" [storeService]="landscapeStoreService">
              </app-authorities-timeline>
            </div>
            <div class="chart-card">
              <app-technology-stack-timeline [storeService]="landscapeStoreService" chartItemHeight="350px">
              </app-technology-stack-timeline>
            </div>
          </div>
          <!--/Result boxes-->

          <div class="info-cards-wrap d-flex" *ngIf="profile && profile?.matched_documents_count > 0">
            <div class="col-2 align-self-center text-end p-0" style="padding-top: 14px !important;">
              <h6 class="m-0">Priority year:</h6>
            </div>
            <div class="col-10">
              <div class="year-slider">
                <ngx-slider [(value)]="startYear" [(highValue)]="endYear" [options]="sliderOptions"
                  (userChangeEnd)="onYearChange($event)"></ngx-slider>
              </div>
            </div>
          </div>
        </div>
      </div>

      <app-patent-list-layout id="landscape-patents-section"
                              [storeService]="landscapeStoreService"
                              [documents]="documents"
                              [menuItems]="tabMenuItems"
                              [isLoading]="isSearching"
                              [showDashboardActionBar]="true"
                              [alwaysShowDocumentsControlBar]="true">
        <ng-container alertMessages [ngTemplateOutlet]="noDocumentsAlert"></ng-container>

        <ng-container additionalTabsContent [ngTemplateOutlet]="additionalTabsContent" *ngIf="isCompetitiveMode"></ng-container>

        <ng-container documentsControlBar [ngTemplateOutlet]="landscapeControlBar"></ng-container>

        <ng-container documentsTable [ngTemplateOutlet]="landscapeList"></ng-container>

        <ng-container documentsVisual *ngIf="!isSearching">
          <app-charts-container [isColumnLayout]="isCombinedMode"
                                [storeService]="landscapeStoreService"></app-charts-container>
        </ng-container>
      </app-patent-list-layout>

      <app-filters-bar
        (filterRemoved)="onFilterRemoved($event)"
        [alwaysBeSticky]="true"
        [storeService]="landscapeStoreService"
        (clearAll)="clearAllFilters()">
      </app-filters-bar>
    </div>

  </div>
  <!-- ./Analysis-->

  <app-zoom-chart [showFavoriteOption]="false" [storeService]="landscapeStoreService"></app-zoom-chart>

  <app-footer></app-footer>

</div>

<app-contact-us-banner></app-contact-us-banner>

<ng-template #landscapeControlBar>
  <div class="lcp-control-bar sticky-top">
    <div class="container-fluid d-flex justify-content-between align-items-center">
      <app-patent-control-bar [columnsToShow]="columnsToShow"
                              [defaultSelectedColumnsToShow]="selectedColumnsCombinedMode"
                              [searchService]="landscapeService"
                              [hasOctiAIControl]="true"
                              [hasFilterListControl]="true" (filterListEvent)="onAdvancedFilter($event)"
                              [hasHarmonizeControl]="true" [hasSaveToCollectionControl]="true"
                              [hasTemporaryLinkControl]="true" [saveSearchTextInput]="profile?.name"
                              [hasExportControl]="true" [exportDisplayOptions]="['csv','xlsx','pdf']"
                              [exportAdditionalParams]="{layout: 'landscape', title: 'Landscape report', subtitle: profile?.name}"
                              [taskShowCreationButton]="true" (taskSaved)="onTaskSaved($event)"
                              [hasSortBySimilarity]="true" (sortBySimilarity)="onSortBySimilarity($event)"
                              [storeService]="landscapeStoreService">
      </app-patent-control-bar>
    </div>
  </div>
</ng-template>

<ng-template #landscapeFooter>
  <div class="d-flex align-items-center justify-content-between py-4"
       [class.combined-footer-section]="isCombinedMode" [hidden]="isSearching || loading">
    <div class="d-flex align-items-center justify-content-start">
      <a routerLink="/landscape" class="btn btn-primary btn-md my-4"><i class="fas fa-arrow-left p-0"></i> Dashboard</a>

      <a (click)="clearSortBySimilarity()" class="btn btn-primary-outline btn-md ms-3 "
         *ngIf="profileStatus == 'READY' && !!sortBySimilarity">
        <i class="fas fa-redo"></i> Clear similarity query
      </a>
    </div>
    <div class="d-flex justify-content-end align-items-center">
      <ng-container *ngIf="pagination && profileStatus == 'READY'">
        <app-page-size [pageSize]="pageSize" (changeSize)="onChangePageSize($event)"
                       [pageOptions]="[25,50,100]"></app-page-size>
        <app-pagination [pagination]="pagination" (navigatePage)="navigate($event)"></app-pagination>
      </ng-container>
    </div>
  </div>
</ng-template>

<ng-template #landscapeList>
  <div [ngClass]="{'container-fluid': !isCombinedMode}">
    <div class="dataTable-wrap result-table2" *ngIf="profileStatus == 'READY'">
      <app-spinner [hidden]="documents !== undefined"></app-spinner>

      <div class="d-flex justify-content-between align-items-center">
        <div [hidden]="documents === undefined || documents?.length === 0" class="new-layout-headline">
          <span [hidden]="!totalSelectedPatents" class="text-green">{{totalSelectedPatents}}</span>{{totalSelectedPatents ? '/' : ''}}{{pagination?.total_hits}}
          {{ 'patent family' | pluralize: pagination?.total_hits }}
          ({{pagination?.total_publications}} {{ 'publication' | pluralize: pagination?.total_publications }})
          in this portfolio
        </div>
        <div class="tools-bar expand-all" *ngIf="documents && documents?.length > 0">
          <a href="javascript:void(0)" class="item-bar" (click)="patentTable.openAll()">
            {{ patentTable.openedPatent.length === documents.length ? 'Collapse all': 'Expand all'}}
            <i class="fas fa-angle-double-down" *ngIf="patentTable.openedPatent.length < documents.length"></i>
            <i class="fas fa-angle-double-up" *ngIf="patentTable.openedPatent.length === documents.length"></i>
          </a>
        </div>
      </div>

      <app-alert type="success" *ngIf="collectionsStoreService.getSaveToCollectionSuccess()"
                 [message]="collectionsStoreService.getSaveToCollectionSuccess()"></app-alert>

      <app-alert type="success" *ngIf="savedTaskMessages" [message]="savedTaskMessages"></app-alert>

      <div class="psr-patent-table">
        <!-- Table -->
        <app-patent-table #patentTable [patents]="documents" (sort)="onSort($event)"
                          [pagination]="pagination" [hasLinksToBooleanSearch]="true"
                          backButtonTitle="Back to landscape" pathUrl="/patent" [linkData]="linkData"
                          [showHighlight]="true" [storeService]="landscapeStoreService">
        </app-patent-table>
        <!-- ./Table -->
      </div>

      <ng-container [ngTemplateOutlet]="landscapeFooter"></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #noDocumentsAlert>
  <div class="container-fluid my-4" *ngIf="profileStatus == 'READY' && documents !== undefined && documents.length === 0">
    <div appNoResults content="It seems we can’t find any result based on your selection.<br>Try to clear or change your filters." *ngIf="hasFilter"></div>

    <div class="alert alert-danger m-0" *ngIf="sortBySimilarity">
      <p >
        Your sort by similarity search returned no results
      </p>
    </div>

    <ng-container [ngTemplateOutlet]="landscapeFooter"></ng-container>
  </div>
</ng-template>

<ng-template #additionalTabsContent>
  <app-competition-benchmark [profile]="profile" *ngIf="isCompetitiveMode"></app-competition-benchmark>
</ng-template>
