.tooltip {
  &.bibliographic-tooltip .tooltip-inner {
    max-width: 500px;

    ul,
    li {
      list-style-type: square;

      &:first-child {
        padding-inline-start: 20px;
      }

      &:not(:first-child) {
        padding-inline-start: 10px;
      }
    }
  }

  &.tooltip-sm {
    .tooltip-inner {
      min-width: 11rem !important;
      max-width: 11rem !important;
      max-height: 25rem !important;
      overflow-y: auto;
    }
  }
  &.tooltip-md {
    .tooltip-inner {
      min-width: 20rem !important;
      max-width: 20rem !important;
      max-height: 25rem !important;
      overflow-y: auto;
    }
  }
  &.tooltip-big {
    .tooltip-inner {
      min-width: 22rem !important;
      max-width: 22rem !important;
      max-height: 35rem !important;
      overflow-y: auto;
    }
  }
  &.tooltip-lg {
    .tooltip-inner {
      min-width: 25rem !important;
      max-width: 35rem !important;
      max-height: 35rem !important;
      overflow-y: auto;
    }
  }

  &.text-left {
    .tooltip-inner {
      text-align: left;
    }
  }
  &.text-center {
    .tooltip-inner {
      text-align: center;
    }
  }
}
