@import './typography';

.popover {
  z-index: 9999 !important;
  &.popover-sm {
    .popover-body {
      min-width: 11rem !important;
      max-width: 11rem !important;
      max-height: 25rem !important;
      overflow-y: auto;
    }
  }
  &.popover-md {
    .popover-inner {
      min-width: 20rem !important;
      max-width: 20rem !important;
      max-height: 25rem !important;
      overflow-y: auto;
    }
  }
  &.popover-big {
    .popover-inner {
      min-width: 22rem !important;
      max-width: 22rem !important;
      max-height: 35rem !important;
      overflow-y: auto;
    }
  }
  &.popover-lg {
    .popover-body {
      min-width: 25rem !important;
      max-width: 35rem !important;
      max-height: 35rem !important;
      overflow-y: auto;
    }
  }
}

.white-popover {
  border-color: transparent;

  .popover-body {
    padding: $spacing-system-spacing-md;
    background-color: $tooltip-bg;
    border-radius: $radius-big;
    color: $tooltip-color;
    box-shadow: 0px 10px 15px -3px rgba(40, 40, 40, 0.10), 0px 0px 6px 0px rgba(40, 40, 40, 0.25) !important;

    a {
      color: $colours-content-content-link
    }
  }

  .popover-title {
    @include add-properties(map-deep-get($typography, heading, h6));
  }

  .popover-description {
    @include add-properties(map-deep-get($typography, body, small));
  }

  .popover-caption {
    @include add-properties(map-deep-get($typography, body, xsmall));
    color: $colours-content-content-secondary;
    padding: $spacing-system-spacing-xx-s 0;
  }

  .popover-close-icon {
    float: right;

    &::before {
      content: "\f00d";
      font-family: 'Font Awesome 6 Pro', serif;
      color: $tooltip-color;
      font-weight: 300;
    }
  }
}

.popover-divider {
  width: 100%;
  margin: 0;
  padding: 0;
  border-bottom: 0.09375rem solid $colours-border-subtle;
  height: 0.09375rem;
}

.info-popover{
  max-width: 450px !important;
  font-size: .75rem;
}

.classification-popover {
  &.popover{
    max-width: 23.4rem;
    min-width: 10rem;
  }

  li {
    list-style: none;
  }
  .popover-body{
    max-height: 35rem;
    overflow-y: auto;
    ::-webkit-scrollbar {
      width: 4px;
    }
  }

  ul {
    padding-left: $spacing-system-spacing-x-s;

    &:first-child {
      margin-bottom: 0;

      & > li {
        font-weight: 600;
        margin-bottom: $spacing-system-spacing-sm;
        padding-bottom: $spacing-system-spacing-sm;
        border-bottom: 0.0625rem solid $colours-border-moderate;
      }
    }

    &:not(:first-child) {
      overflow-y: auto;
      span {
        font-weight: 600;
      }

      border-left: 0.0625rem solid $colours-border-moderate;
      margin-top: $spacing-system-spacing-sm;
    }
  }
  @media screen and (max-height: 1079px) {
    .popover-body{
      overflow-y: unset
    }
    ul:first-child > ul{
      max-height: 23rem !important;
      overflow-y: auto;
      padding-right: $spacing-system-spacing-xxx-s;
    }
  }
}

.context-menu-popper, .inline-menu-popper {
  box-shadow: 0rem 0.5rem 0.75rem 0.5rem rgba(40, 40, 40, 0.24);

  .popover-arrow {
    display: none;
  }
}
.context-menu-popper {
  .popover-body {
    padding: $spacing-system-spacing-md !important;
  }
}
.inline-menu-popper {
  .popover-body {
    padding: $spacing-system-spacing-none !important;
  }
}

.green-patent-popover {
  ul {
    padding-left: 15px;
  }

  .categories-text {
    display: flex;
  }
}

@media (max-height: 768px) {
  .green-patent-popover .popover-body {
    margin-top: 100px !important;
  }
  .green-patent-popover .popover-descriptions {
    max-height: 360px;
    overflow-y: auto;
  }
}

.collection-sources-popover{
  max-width: 23.4rem;
  min-width: 10rem;
  .source-query{
    background-color: $colours-background-bg-secondary;
    padding: $spacing-system-spacing-sm;
    border: 1px solid $colours-border-subtle;
    border-radius: $radius-sm;
    color: $colours-content-content-secondary;
    @include add-properties(map-deep-get($typography, body, small));
    .statement{
      color: $colours-content-content-active;
      @include add-properties(map-deep-get($typography, heading, h6));
    }
    &-patents{
      display: flex;
      gap: $spacing-system-spacing-xx-s;
      align-items: center;
      flex-wrap: wrap;
    }
    &-added{
      padding: $spacing-system-spacing-xxx-s $spacing-system-spacing-xx-s $spacing-system-spacing-sm;
      color: $colours-content-content-tertiary;
    }
  }
  .popover-descriptions{
    padding: $spacing-system-spacing-xx-s;
  }
  .sources-container{
    max-height: 303px;
    overflow-y: auto;
    padding-right: $spacing-system-spacing-xx-s;
    border-radius: $radius-sm;
    .popover-descriptions:hover{
      background-color: $colours-background-bg-secondary;
    }
  }
  .popover-container{
    border-radius: $radius-sm;
    padding: $spacing-system-spacing-xx-s;
    &:hover{
      background-color: $colours-background-bg-secondary;
    }
  }
}

.comment-popper{
  &.popper-instance{
    padding: $spacing-system-spacing-md $spacing-system-spacing-md 0 !important;
  }

  .popper-inner{
    width: 361px;
    max-width: 361px;
  }
}

.authority-legal-status-popover {
  .popover-body{
    min-width: 300px;
    max-width: 336px;
    width: max-content;
    padding: 0;
  }
}

.authority-popover {
  .popover-body {
    width: 390px;
    max-height: 335px;
    overflow: hidden auto;
    .authority-popover-publication {
      display: flex;
      justify-content: space-between;
      padding: $spacing-system-spacing-xx-s $spacing-system-spacing-sm;
      margin-bottom: $spacing-system-spacing-xx-s;
      border-radius: $radius-sm;

      .open-family-new-tab {
        visibility: hidden;
      }
      &:hover {
        background-color: $colours-background-bg-secondary;
        .open-family-new-tab {
          visibility: visible;
        }
      }
    }
  }
}

.octi-ai-popover{
  .popover-body{
    padding: $spacing-system-spacing-xx-s;
  }
}

.permission-setting-popper {
  .popper-inner {
    max-width: 14.5rem !important;
  }
}
