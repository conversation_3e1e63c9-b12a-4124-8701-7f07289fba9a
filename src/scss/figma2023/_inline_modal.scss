::ng-deep {
  .tooltip-inner, .popper-inner, .popper-instance {
    &:has(.inline-modal-container) {
      padding: 0 !important;
      margin: 0 !important;
    }
  }
}

.inline-modal-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;
  width: 100%;

  .inline-modal-block {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-content: flex-start;
    align-items: flex-start;
    width: 100%;
    gap: $spacing-system-spacing-sm;
    padding: $spacing-system-spacing-md;

    &.gap-spacing-md {
      gap: $spacing-system-spacing-md !important;
    }

    &.gap-spacing-x-s {
      gap: $spacing-system-spacing-x-s !important;
    }

    &.gap-spacing-xx-s {
      gap: $spacing-system-spacing-xx-s !important;
    }

    &.justify-content-end {
      justify-content: flex-end !important;
    }

    &.flex-row {
      flex-direction: row !important;
    }

    &.p-y-spacing-big {
      padding-top: $spacing-system-spacing-big !important;
      padding-bottom: $spacing-system-spacing-big !important;
    }

    &.p-t-spacing-big {
      padding-top: $spacing-system-spacing-big !important;
    }

    &.p-b-spacing-big {
      padding-bottom: $spacing-system-spacing-big !important;
    }
  }
}
